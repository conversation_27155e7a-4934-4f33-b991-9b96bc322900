# NetX Crypto AES Demo and Debug Program

这个项目演示了如何使用NetX Duo加密库进行AES加密和解密操作，并提供了调试功能。

## 项目结构

```
crypto_libraries/
├── CMakeLists.txt          # CMake构建配置
├── main.c                  # 主程序，包含AES测试和调试功能
├── nx_crypto_config.h      # 配置头文件
├── README.md              # 本文档
├── inc/                   # 头文件目录
├── src/                   # 源文件目录
├── ports/                 # 平台相关代码
└── build/                 # 构建输出目录
```

## 功能特性

### 1. AES测试功能
- **AES-128 CBC模式测试**: 使用128位密钥进行CBC模式加密解密测试
- **AES-256 CBC模式测试**: 使用256位密钥进行CBC模式加密解密测试
- **基础功能验证**: 验证AES加密解密的基本功能

### 2. 交互式调试模式
- 支持用户选择AES-128或AES-256
- 使用预定义的测试向量进行加密解密
- 显示详细的十六进制输出，便于调试

### 3. 调试友好的输出
- 十六进制格式显示密钥、IV、明文、密文
- 清晰的测试结果显示
- 详细的错误信息

## 编译和运行

### 前提条件
- CMake 3.10或更高版本
- GCC或Clang编译器
- macOS、Linux或其他Unix-like系统

### 编译步骤

1. 创建构建目录并进入：
```bash
mkdir -p build
cd build
```

2. 运行CMake配置：
```bash
cmake ..
```

3. 编译项目：
```bash
make
```

### 运行程序

```bash
./nx_crypto_demo
```

## 调试指南

### 1. 使用GDB调试

```bash
# 编译调试版本
cd build
cmake -DCMAKE_BUILD_TYPE=Debug ..
make

# 使用GDB启动程序
gdb ./nx_crypto_demo

# 在GDB中设置断点
(gdb) break _nx_crypto_aes_key_set
(gdb) break _nx_crypto_aes_encrypt
(gdb) break _nx_crypto_aes_decrypt
(gdb) run
```

### 2. 关键调试点

程序中可以设置断点的关键函数：

- `_nx_crypto_aes_key_set()`: AES密钥设置
- `_nx_crypto_aes_encrypt()`: AES加密
- `_nx_crypto_aes_decrypt()`: AES解密
- `_nx_crypto_method_aes_operation()`: AES操作入口点

### 3. 查看内存和变量

在GDB中可以查看的重要变量：
```bash
# 查看AES上下文
(gdb) print aes_ctx

# 查看密钥调度
(gdb) print aes_ctx.nx_crypto_aes_key_schedule

# 查看输入输出数据
(gdb) x/16xb plaintext
(gdb) x/16xb ciphertext
```

## 程序输出示例

```
=================================================
    NetX Crypto AES Demo and Debug Program
=================================================
Initializing NetX Crypto library...
✓ NetX Crypto library initialized successfully

=== AES-128 CBC Mode Test ===
Key: 2B 7E 15 16 28 AE D2 A6  AB F7 15 88 09 CF 4F 3C
IV: 00 01 02 03 04 05 06 07  08 09 0A 0B 0C 0D 0E 0F
Plaintext: 6B C1 BE E2 2E 40 9F 96  E9 3D 7E 11 73 93 17 2A
AES key set successfully
Ciphertext: 3A D7 7B B4 0D 7A 36 60  A8 9E CA F3 24 66 EF 97
Decrypted: 6B C1 BE E2 2E 40 9F 96  E9 3D 7E 11 73 93 17 2A
✓ AES-128 CBC test PASSED

=== Test Summary ===
Total tests: 3
Passed tests: 3
Failed tests: 0
✓ All tests PASSED!
```

## 技术细节

### AES实现特性
- 支持AES-128、AES-192、AES-256
- 支持CBC、CTR、GCM、CCM模式
- 硬件加速支持（如果可用）
- 符合FIPS标准

### 配置选项
- `NX_CRYPTO_STANDALONE_ENABLE`: 启用独立模式
- `NX_CRYPTO_SELF_TEST`: 启用自测试功能
- `NX_CRYPTO_LITTLE_ENDIAN`: 设置字节序

## 故障排除

### 常见问题

1. **编译错误**: 确保安装了CMake和GCC
2. **链接错误**: 检查是否包含了所有必要的源文件
3. **运行时错误**: 检查内存对齐和缓冲区大小

### 调试技巧

1. 使用`print_hex()`函数查看数据
2. 检查函数返回值
3. 验证密钥和IV的正确性
4. 确保数据长度是16字节的倍数（对于CBC模式）

## 扩展功能

可以基于此项目扩展的功能：
- 添加更多AES模式（ECB、OFB、CFB）
- 实现文件加密解密
- 添加性能测试
- 集成其他加密算法
- 添加网络通信加密示例

## 许可证

本项目基于MIT许可证，详见源代码中的许可证声明。
