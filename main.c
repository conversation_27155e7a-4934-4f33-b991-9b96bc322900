//
// NetX Crypto AES Demo and Debug Program
// Created by 1mnoi on 2025/9/28.
//

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>

// 配置文件
#include "nx_crypto_config.h"

// NetX Crypto includes
#include "nx_crypto.h"
#include "nx_crypto_aes.h"

// 辅助函数：打印十六进制数据
void print_hex(const char* label, const unsigned char* data, size_t len) {
    printf("%s: ", label);
    for (size_t i = 0; i < len; i++) {
        printf("%02X", data[i]);
        if ((i + 1) % 16 == 0) {
            printf("\n");
            if (i + 1 < len) {
                printf("    ");
            }
        } else if ((i + 1) % 8 == 0) {
            printf("  ");
        } else {
            printf(" ");
        }
    }
    if (len % 16 != 0) {
        printf("\n");
    }
}

// 比较两个数组是否相等
int compare_arrays(const unsigned char* a, const unsigned char* b, size_t len) {
    for (size_t i = 0; i < len; i++) {
        if (a[i] != b[i]) {
            return 0;
        }
    }
    return 1;
}

// AES-128 CBC 模式测试
int test_aes_128_cbc() {
    printf("\n=== AES-128 CBC Mode Test ===\n");

    // 测试数据
    unsigned char key[16] = {
        0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
        0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
    };

    unsigned char iv[16] = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f
    };

    unsigned char plaintext[16] = {
        0x6b, 0xc1, 0xbe, 0xe2, 0x2e, 0x40, 0x9f, 0x96,
        0xe9, 0x3d, 0x7e, 0x11, 0x73, 0x93, 0x17, 0x2a
    };

    unsigned char ciphertext[16];
    unsigned char decrypted[16];

    // AES上下文
    NX_CRYPTO_AES aes_ctx;
    memset(&aes_ctx, 0, sizeof(aes_ctx));

    print_hex("Key", key, 16);
    print_hex("IV", iv, 16);
    print_hex("Plaintext", plaintext, 16);

    // 设置密钥
    UINT status = _nx_crypto_aes_key_set(&aes_ctx, key, NX_CRYPTO_AES_KEY_SIZE_128_BITS);
    if (status != NX_CRYPTO_SUCCESS) {
        printf("Error: Failed to set AES key, status = %u\n", status);
        return -1;
    }

    printf("AES key set successfully\n");

    // 使用CBC模式加密
    memcpy(aes_ctx.nx_crypto_aes_mode_context.cbc.nx_crypto_cbc_last_block, iv, 16);

    // 执行加密
    status = _nx_crypto_aes_encrypt(&aes_ctx, plaintext, ciphertext, 16);
    if (status != NX_CRYPTO_SUCCESS) {
        printf("Error: AES encryption failed, status = %u\n", status);
        return -1;
    }

    print_hex("Ciphertext", ciphertext, 16);

    // 执行解密
    memcpy(aes_ctx.nx_crypto_aes_mode_context.cbc.nx_crypto_cbc_last_block, iv, 16);
    status = _nx_crypto_aes_decrypt(&aes_ctx, ciphertext, decrypted, 16);
    if (status != NX_CRYPTO_SUCCESS) {
        printf("Error: AES decryption failed, status = %u\n", status);
        return -1;
    }

    print_hex("Decrypted", decrypted, 16);

    // 验证解密结果
    if (compare_arrays(plaintext, decrypted, 16)) {
        printf("✓ AES-128 CBC test PASSED\n");
        return 0;
    } else {
        printf("✗ AES-128 CBC test FAILED\n");
        return -1;
    }
}

// AES-256 CBC 模式测试
int test_aes_256_cbc() {
    printf("\n=== AES-256 CBC Mode Test ===\n");

    // 256位密钥
    unsigned char key[32] = {
        0x60, 0x3d, 0xeb, 0x10, 0x15, 0xca, 0x71, 0xbe,
        0x2b, 0x73, 0xae, 0xf0, 0x85, 0x7d, 0x77, 0x81,
        0x1f, 0x35, 0x2c, 0x07, 0x3b, 0x61, 0x08, 0xd7,
        0x2d, 0x98, 0x10, 0xa3, 0x09, 0x14, 0xdf, 0xf4
    };

    unsigned char iv[16] = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f
    };

    unsigned char plaintext[16] = {
        0x6b, 0xc1, 0xbe, 0xe2, 0x2e, 0x40, 0x9f, 0x96,
        0xe9, 0x3d, 0x7e, 0x11, 0x73, 0x93, 0x17, 0x2a
    };

    unsigned char ciphertext[16];
    unsigned char decrypted[16];

    // AES上下文
    NX_CRYPTO_AES aes_ctx;
    memset(&aes_ctx, 0, sizeof(aes_ctx));

    print_hex("Key (256-bit)", key, 32);
    print_hex("IV", iv, 16);
    print_hex("Plaintext", plaintext, 16);

    // 设置256位密钥
    UINT status = _nx_crypto_aes_key_set(&aes_ctx, key, NX_CRYPTO_AES_KEY_SIZE_256_BITS);
    if (status != NX_CRYPTO_SUCCESS) {
        printf("Error: Failed to set AES-256 key, status = %u\n", status);
        return -1;
    }

    printf("AES-256 key set successfully\n");

    // 使用CBC模式加密
    memcpy(aes_ctx.nx_crypto_aes_mode_context.cbc.nx_crypto_cbc_last_block, iv, 16);

    // 执行加密
    status = _nx_crypto_aes_encrypt(&aes_ctx, plaintext, ciphertext, 16);
    if (status != NX_CRYPTO_SUCCESS) {
        printf("Error: AES-256 encryption failed, status = %u\n", status);
        return -1;
    }

    print_hex("Ciphertext", ciphertext, 16);

    // 执行解密
    memcpy(aes_ctx.nx_crypto_aes_mode_context.cbc.nx_crypto_cbc_last_block, iv, 16);
    status = _nx_crypto_aes_decrypt(&aes_ctx, ciphertext, decrypted, 16);
    if (status != NX_CRYPTO_SUCCESS) {
        printf("Error: AES-256 decryption failed, status = %u\n", status);
        return -1;
    }

    print_hex("Decrypted", decrypted, 16);

    // 验证解密结果
    if (compare_arrays(plaintext, decrypted, 16)) {
        printf("✓ AES-256 CBC test PASSED\n");
        return 0;
    } else {
        printf("✗ AES-256 CBC test FAILED\n");
        return -1;
    }
}

// 简单的AES功能验证
int verify_aes_functionality() {
    printf("\n=== AES Functionality Verification ===\n");

    // 测试数据
    unsigned char key[16] = {
        0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
        0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
    };

    unsigned char plaintext[16] = {
        0x32, 0x43, 0xf6, 0xa8, 0x88, 0x5a, 0x30, 0x8d,
        0x31, 0x31, 0x98, 0xa2, 0xe0, 0x37, 0x07, 0x34
    };

    unsigned char ciphertext[16];
    unsigned char decrypted[16];

    // AES上下文
    NX_CRYPTO_AES aes_ctx;
    memset(&aes_ctx, 0, sizeof(aes_ctx));

    printf("Verifying basic AES-128 encrypt/decrypt...\n");

    // 设置密钥
    UINT status = _nx_crypto_aes_key_set(&aes_ctx, key, NX_CRYPTO_AES_KEY_SIZE_128_BITS);
    if (status != NX_CRYPTO_SUCCESS) {
        printf("✗ Failed to set AES key\n");
        return -1;
    }

    // 加密
    status = _nx_crypto_aes_encrypt(&aes_ctx, plaintext, ciphertext, 16);
    if (status != NX_CRYPTO_SUCCESS) {
        printf("✗ AES encryption failed\n");
        return -1;
    }

    // 解密
    status = _nx_crypto_aes_decrypt(&aes_ctx, ciphertext, decrypted, 16);
    if (status != NX_CRYPTO_SUCCESS) {
        printf("✗ AES decryption failed\n");
        return -1;
    }

    // 验证
    if (compare_arrays(plaintext, decrypted, 16)) {
        printf("✓ AES functionality verification PASSED\n");
        return 0;
    } else {
        printf("✗ AES functionality verification FAILED\n");
        return -1;
    }
}

// 交互式AES调试功能
void interactive_aes_debug() {
    printf("\n=== Interactive AES Debug Mode ===\n");
    printf("This mode allows you to input custom data for AES encryption/decryption\n");

    char input[256];
    unsigned char key[32];
    unsigned char iv[16];
    unsigned char data[64];
    unsigned char output[64];
    int key_size, data_len;

    printf("\nSelect key size:\n");
    printf("1. AES-128 (16 bytes)\n");
    printf("2. AES-256 (32 bytes)\n");
    printf("Enter choice (1 or 2): ");

    if (fgets(input, sizeof(input), stdin) == NULL) {
        printf("Error reading input\n");
        return;
    }

    int choice = atoi(input);
    if (choice == 1) {
        key_size = 16;
        printf("Using AES-128\n");
    } else if (choice == 2) {
        key_size = 32;
        printf("Using AES-256\n");
    } else {
        printf("Invalid choice\n");
        return;
    }

    // 使用默认测试密钥和IV
    if (key_size == 16) {
        unsigned char default_key[16] = {
            0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
            0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
        };
        memcpy(key, default_key, 16);
    } else {
        unsigned char default_key[32] = {
            0x60, 0x3d, 0xeb, 0x10, 0x15, 0xca, 0x71, 0xbe,
            0x2b, 0x73, 0xae, 0xf0, 0x85, 0x7d, 0x77, 0x81,
            0x1f, 0x35, 0x2c, 0x07, 0x3b, 0x61, 0x08, 0xd7,
            0x2d, 0x98, 0x10, 0xa3, 0x09, 0x14, 0xdf, 0xf4
        };
        memcpy(key, default_key, 32);
    }

    unsigned char default_iv[16] = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f
    };
    memcpy(iv, default_iv, 16);

    // 使用默认测试数据
    unsigned char default_data[16] = {
        0x6b, 0xc1, 0xbe, 0xe2, 0x2e, 0x40, 0x9f, 0x96,
        0xe9, 0x3d, 0x7e, 0x11, 0x73, 0x93, 0x17, 0x2a
    };
    memcpy(data, default_data, 16);
    data_len = 16;

    print_hex("Using Key", key, key_size);
    print_hex("Using IV", iv, 16);
    print_hex("Using Data", data, data_len);

    // 创建AES上下文
    NX_CRYPTO_AES aes_ctx;
    memset(&aes_ctx, 0, sizeof(aes_ctx));

    // 设置密钥
    UINT key_size_bits = (key_size == 16) ? NX_CRYPTO_AES_KEY_SIZE_128_BITS : NX_CRYPTO_AES_KEY_SIZE_256_BITS;
    UINT status = _nx_crypto_aes_key_set(&aes_ctx, key, key_size_bits);
    if (status != NX_CRYPTO_SUCCESS) {
        printf("Error: Failed to set AES key, status = %u\n", status);
        return;
    }

    // 加密
    printf("\nPerforming encryption...\n");
    status = _nx_crypto_aes_encrypt(&aes_ctx, data, output, data_len);
    if (status != NX_CRYPTO_SUCCESS) {
        printf("Error: AES encryption failed, status = %u\n", status);
        return;
    }

    print_hex("Encrypted Data", output, data_len);

    // 解密
    printf("\nPerforming decryption...\n");
    unsigned char decrypted[64];
    status = _nx_crypto_aes_decrypt(&aes_ctx, output, decrypted, data_len);
    if (status != NX_CRYPTO_SUCCESS) {
        printf("Error: AES decryption failed, status = %u\n", status);
        return;
    }

    print_hex("Decrypted Data", decrypted, data_len);

    // 验证
    if (compare_arrays(data, decrypted, data_len)) {
        printf("✓ Encryption/Decryption verification PASSED\n");
    } else {
        printf("✗ Encryption/Decryption verification FAILED\n");
    }
}

// 主函数
int main() {
    printf("=================================================\n");
    printf("    NetX Crypto AES Demo and Debug Program\n");
    printf("=================================================\n");

    // 初始化NetX Crypto库
    printf("Initializing NetX Crypto library...\n");
    UINT status = _nx_crypto_initialize();
    if (status != NX_CRYPTO_SUCCESS) {
        printf("Error: Failed to initialize NetX Crypto library, status = %u\n", status);
        return -1;
    }
    printf("✓ NetX Crypto library initialized successfully\n");

    int total_tests = 0;
    int passed_tests = 0;

    // 运行AES-128 CBC测试
    total_tests++;
    if (test_aes_128_cbc() == 0) {
        passed_tests++;
    }

    // 运行AES-256 CBC测试
    total_tests++;
    if (test_aes_256_cbc() == 0) {
        passed_tests++;
    }

    // 运行AES功能验证
    total_tests++;
    if (verify_aes_functionality() == 0) {
        passed_tests++;
    }

    // 显示测试结果摘要
    printf("\n=== Test Summary ===\n");
    printf("Total tests: %d\n", total_tests);
    printf("Passed tests: %d\n", passed_tests);
    printf("Failed tests: %d\n", total_tests - passed_tests);

    if (passed_tests == total_tests) {
        printf("✓ All tests PASSED!\n");
    } else {
        printf("✗ Some tests FAILED!\n");
    }

    // 交互式调试模式
    printf("\n=== Interactive Mode ===\n");
    printf("Would you like to enter interactive AES debug mode? (y/n): ");

    char input[10];
    if (fgets(input, sizeof(input), stdin) != NULL) {
        if (input[0] == 'y' || input[0] == 'Y') {
            interactive_aes_debug();
        }
    }

    printf("\nProgram completed.\n");
    printf("You can now set breakpoints and debug the AES functions:\n");
    printf("- _nx_crypto_aes_key_set()\n");
    printf("- _nx_crypto_aes_encrypt()\n");
    printf("- _nx_crypto_aes_decrypt()\n");
    printf("- _nx_crypto_method_aes_operation()\n");

    return (passed_tests == total_tests) ? 0 : 1;
}