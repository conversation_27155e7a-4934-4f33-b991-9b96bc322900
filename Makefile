# NetX Crypto AES Demo Makefile
# Alternative to CMake for simple compilation

# Compiler settings
CC = gcc
CFLAGS = -Wall -Wextra -g -O0 -std=c99
DEFINES = -DNX_CRYPTO_STANDALONE_ENABLE -DNX_CRYPTO_SELF_TEST -DNX_CRYPTO_LITTLE_ENDIAN=1

# Directories
SRC_DIR = src
INC_DIR = inc
PORT_DIR = ports/linux/gnu/inc
BUILD_DIR = build

# Include paths
INCLUDES = -I. -I$(INC_DIR) -I$(PORT_DIR)

# Source files
SOURCES = main.c \
          $(SRC_DIR)/nx_crypto_aes.c \
          $(SRC_DIR)/nx_crypto_cbc.c \
          $(SRC_DIR)/nx_crypto_ctr.c \
          $(SRC_DIR)/nx_crypto_gcm.c \
          $(SRC_DIR)/nx_crypto_ccm.c \
          $(SRC_DIR)/nx_crypto_xcbc_mac.c \
          $(SRC_DIR)/nx_crypto_initialize.c \
          $(SRC_DIR)/nx_crypto_null_cipher.c

# Object files
OBJECTS = $(SOURCES:%.c=$(BUILD_DIR)/%.o)

# Target executable
TARGET = $(BUILD_DIR)/nx_crypto_demo

# Default target
all: $(TARGET)

# Create build directory
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)
	mkdir -p $(BUILD_DIR)/$(SRC_DIR)

# Link executable
$(TARGET): $(BUILD_DIR) $(OBJECTS)
	$(CC) $(OBJECTS) -o $@ -lm

# Compile source files
$(BUILD_DIR)/%.o: %.c
	$(CC) $(CFLAGS) $(DEFINES) $(INCLUDES) -c $< -o $@

# Clean build files
clean:
	rm -rf $(BUILD_DIR)

# Run the program
run: $(TARGET)
	$(TARGET)

# Debug with GDB
debug: $(TARGET)
	gdb $(TARGET)

# Show help
help:
	@echo "Available targets:"
	@echo "  all     - Build the program (default)"
	@echo "  clean   - Remove build files"
	@echo "  run     - Build and run the program"
	@echo "  debug   - Build and run with GDB"
	@echo "  help    - Show this help message"

# Phony targets
.PHONY: all clean run debug help

# Dependencies (simplified)
$(BUILD_DIR)/main.o: main.c nx_crypto_config.h $(INC_DIR)/nx_crypto.h $(INC_DIR)/nx_crypto_aes.h
$(BUILD_DIR)/$(SRC_DIR)/nx_crypto_aes.o: $(SRC_DIR)/nx_crypto_aes.c $(INC_DIR)/nx_crypto_aes.h
$(BUILD_DIR)/$(SRC_DIR)/nx_crypto_cbc.o: $(SRC_DIR)/nx_crypto_cbc.c $(INC_DIR)/nx_crypto_cbc.h
$(BUILD_DIR)/$(SRC_DIR)/nx_crypto_ctr.o: $(SRC_DIR)/nx_crypto_ctr.c $(INC_DIR)/nx_crypto_ctr.h
$(BUILD_DIR)/$(SRC_DIR)/nx_crypto_gcm.o: $(SRC_DIR)/nx_crypto_gcm.c $(INC_DIR)/nx_crypto_gcm.h
$(BUILD_DIR)/$(SRC_DIR)/nx_crypto_ccm.o: $(SRC_DIR)/nx_crypto_ccm.c $(INC_DIR)/nx_crypto_ccm.h
$(BUILD_DIR)/$(SRC_DIR)/nx_crypto_xcbc_mac.o: $(SRC_DIR)/nx_crypto_xcbc_mac.c $(INC_DIR)/nx_crypto_xcbc_mac.h
$(BUILD_DIR)/$(SRC_DIR)/nx_crypto_initialize.o: $(SRC_DIR)/nx_crypto_initialize.c $(INC_DIR)/nx_crypto.h
$(BUILD_DIR)/$(SRC_DIR)/nx_crypto_null_cipher.o: $(SRC_DIR)/nx_crypto_null_cipher.c $(INC_DIR)/nx_crypto_null.h
