#ifndef NX_CRYPTO_CONFIG_H
#define NX_CRYPTO_CONFIG_H

// 启用独立模式
#define NX_CRYPTO_STANDALONE_ENABLE

// 启用自测试
#define NX_CRYPTO_SELF_TEST

// 设置字节序
#define NX_CRYPTO_LITTLE_ENDIAN 1

// 内存操作宏定义
#ifdef NX_CRYPTO_SELF_TEST
#define NX_CRYPTO_MEMCPY    memcpy
#define NX_CRYPTO_MEMMOVE   memmove
#define NX_CRYPTO_MEMSET    memset
#define NX_CRYPTO_MEMCMP    memcmp
#else
#define NX_CRYPTO_MEMCPY    memcpy
#define NX_CRYPTO_MEMMOVE   memmove
#define NX_CRYPTO_MEMSET    memset
#define NX_CRYPTO_MEMCMP    memcmp
#endif

// 状态检查宏（在独立模式下为空）
#define NX_CRYPTO_STATE_CHECK

// 硬件随机数初始化（在独立模式下为空）
#define NX_CRYPTO_HARDWARE_RAND_INITIALIZE

// 完整性测试（在独立模式下为空）
#define NX_CRYPTO_INTEGRITY_TEST

// 功能测试检查宏
#define NX_CRYPTO_FUNCTIONAL_TEST_CHECK(status) \
    if (status != 0) { \
        printf("Functional test failed at line %d with status %d\n", __LINE__, status); \
        return status; \
    }

// 参数未使用宏
#define NX_CRYPTO_PARAMETER_NOT_USED(x) ((void)(x))

// 保持符号宏
#define NX_CRYPTO_KEEP

// 空指针定义
#ifndef NX_CRYPTO_NULL
#define NX_CRYPTO_NULL ((void*)0)
#endif

// 成功状态码
#ifndef NX_CRYPTO_SUCCESS
#define NX_CRYPTO_SUCCESS 0
#endif

// 错误状态码
#ifndef NX_CRYPTO_PTR_ERROR
#define NX_CRYPTO_PTR_ERROR 1
#endif

#ifndef NX_CRYPTO_NOT_SUCCESSFUL
#define NX_CRYPTO_NOT_SUCCESSFUL 2
#endif

#ifndef NX_CRYPTO_UNSUPPORTED_KEY_SIZE
#define NX_CRYPTO_UNSUPPORTED_KEY_SIZE 3
#endif

// 密钥大小定义
#define NX_CRYPTO_AES_128_KEY_LEN_IN_BITS 128
#define NX_CRYPTO_AES_192_KEY_LEN_IN_BITS 192
#define NX_CRYPTO_AES_256_KEY_LEN_IN_BITS 256

// 认证ICV截断位数
#define NX_CRYPTO_AUTHENTICATION_ICV_TRUNC_BITS 128

#endif /* NX_CRYPTO_CONFIG_H */
