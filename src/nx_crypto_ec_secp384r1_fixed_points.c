/***************************************************************************
 * Copyright (c) 2024 Microsoft Corporation 
 * Copyright (c) 2025-present Eclipse ThreadX Contributors
 * 
 * This program and the accompanying materials are made available under the
 * terms of the MIT License which is available at
 * https://opensource.org/licenses/MIT.
 * 
 * SPDX-License-Identifier: MIT
 **************************************************************************/


/**************************************************************************/
/**************************************************************************/
/**                                                                       */
/** NetX Crypto Component                                                 */
/**                                                                       */
/**   Elliptical Curve Cryptography                                       */
/**                                                                       */
/**************************************************************************/
/**************************************************************************/


#include "nx_crypto_ec.h"
static NX_CRYPTO_CONST HN_UBASE           secp384r1_fixed_points_data[][48 >> HN_SIZE_SHIFT] =
{

    /* 2G.x */
    {
        HN_ULONG_TO_UBASE(0x574A2D7A), HN_ULONG_TO_UBASE(0x214A5541),
        HN_ULONG_TO_UBASE(0x0BAFF67E), HN_ULONG_TO_UBASE(0x8BB26B1F),
        HN_ULONG_TO_UBASE(0x685CB49E), HN_ULONG_TO_UBASE(0xE8E8A314),
        HN_ULONG_TO_UBASE(0x05F1DBE9), HN_ULONG_TO_UBASE(0x6AD56435),
        HN_ULONG_TO_UBASE(0x415B4393), HN_ULONG_TO_UBASE(0xB2128765),
        HN_ULONG_TO_UBASE(0xE52E83A1), HN_ULONG_TO_UBASE(0xFDFF5D78)
    },

    /* 2G.y */
    {
        HN_ULONG_TO_UBASE(0x978E2B11), HN_ULONG_TO_UBASE(0xE715E976),
        HN_ULONG_TO_UBASE(0xD4D391B8), HN_ULONG_TO_UBASE(0xDCC72E10),
        HN_ULONG_TO_UBASE(0xDD2D7EC4), HN_ULONG_TO_UBASE(0xEF01A9D8),
        HN_ULONG_TO_UBASE(0x5963C951), HN_ULONG_TO_UBASE(0x00377F99),
        HN_ULONG_TO_UBASE(0xF10B944A), HN_ULONG_TO_UBASE(0x13F5D41F),
        HN_ULONG_TO_UBASE(0x7857AA4C), HN_ULONG_TO_UBASE(0x4DB0BC42)
    },

    /* 3G.x */
    {
        HN_ULONG_TO_UBASE(0x8E8CF6BD), HN_ULONG_TO_UBASE(0x4DF624DB),
        HN_ULONG_TO_UBASE(0x8547E6B6), HN_ULONG_TO_UBASE(0x8244132B),
        HN_ULONG_TO_UBASE(0xEAAC9420), HN_ULONG_TO_UBASE(0xA9D5E399),
        HN_ULONG_TO_UBASE(0x21AD8066), HN_ULONG_TO_UBASE(0x0A9B91BD),
        HN_ULONG_TO_UBASE(0x3EEE915B), HN_ULONG_TO_UBASE(0x492ECEBD),
        HN_ULONG_TO_UBASE(0x0FDD804E), HN_ULONG_TO_UBASE(0x5E54D953)
    },

    /* 3G.y */
    {
        HN_ULONG_TO_UBASE(0xCC5A43B2), HN_ULONG_TO_UBASE(0x44288C00),
        HN_ULONG_TO_UBASE(0x42727FD7), HN_ULONG_TO_UBASE(0xF66D7125),
        HN_ULONG_TO_UBASE(0x89A66C33), HN_ULONG_TO_UBASE(0x6F98B352),
        HN_ULONG_TO_UBASE(0x95821B09), HN_ULONG_TO_UBASE(0x5009A4B4),
        HN_ULONG_TO_UBASE(0x0E8131D0), HN_ULONG_TO_UBASE(0xB5E534AC),
        HN_ULONG_TO_UBASE(0x4BA24BC0), HN_ULONG_TO_UBASE(0x4A3D7763)
    },

    /* 4G.x */
    {
        HN_ULONG_TO_UBASE(0xE0BDE8C2), HN_ULONG_TO_UBASE(0xD490B021),
        HN_ULONG_TO_UBASE(0xCA52B096), HN_ULONG_TO_UBASE(0x6CC28A6C),
        HN_ULONG_TO_UBASE(0xC30BD659), HN_ULONG_TO_UBASE(0xCF1DF4FD),
        HN_ULONG_TO_UBASE(0x50FEAF12), HN_ULONG_TO_UBASE(0x0E63F460),
        HN_ULONG_TO_UBASE(0x7F52C6E5), HN_ULONG_TO_UBASE(0xCD958F85),
        HN_ULONG_TO_UBASE(0x34CB8FA1), HN_ULONG_TO_UBASE(0x2913D4EB)
    },

    /* 4G.y */
    {
        HN_ULONG_TO_UBASE(0xB083DCB0), HN_ULONG_TO_UBASE(0xCBF987E9),
        HN_ULONG_TO_UBASE(0xB1A874D8), HN_ULONG_TO_UBASE(0xB47F863A),
        HN_ULONG_TO_UBASE(0xB3BB7DA7), HN_ULONG_TO_UBASE(0x2D48722E),
        HN_ULONG_TO_UBASE(0x7603FD5F), HN_ULONG_TO_UBASE(0x27855D53),
        HN_ULONG_TO_UBASE(0x7765A132), HN_ULONG_TO_UBASE(0xA5CCE5A0),
        HN_ULONG_TO_UBASE(0x5A14FFB1), HN_ULONG_TO_UBASE(0x047B885F)
    },

    /* 5G.x */
    {
        HN_ULONG_TO_UBASE(0x931694D6), HN_ULONG_TO_UBASE(0x3311EC54),
        HN_ULONG_TO_UBASE(0xD26C55B2), HN_ULONG_TO_UBASE(0x66004EC3),
        HN_ULONG_TO_UBASE(0x1F2CCD66), HN_ULONG_TO_UBASE(0xD50A0AC4),
        HN_ULONG_TO_UBASE(0x4B047385), HN_ULONG_TO_UBASE(0x274E6260),
        HN_ULONG_TO_UBASE(0xB7FD6664), HN_ULONG_TO_UBASE(0xD96204E4),
        HN_ULONG_TO_UBASE(0x6AA71294), HN_ULONG_TO_UBASE(0xD23B746B)
    },

    /* 5G.y */
    {
        HN_ULONG_TO_UBASE(0x46B64ADD), HN_ULONG_TO_UBASE(0x9A7231A7),
        HN_ULONG_TO_UBASE(0xBE780847), HN_ULONG_TO_UBASE(0x47709B8E),
        HN_ULONG_TO_UBASE(0xAA3AEC73), HN_ULONG_TO_UBASE(0xC5BE101D),
        HN_ULONG_TO_UBASE(0xB89D3090), HN_ULONG_TO_UBASE(0x2786BD19),
        HN_ULONG_TO_UBASE(0x09A71BA8), HN_ULONG_TO_UBASE(0x5F348F1D),
        HN_ULONG_TO_UBASE(0x0169076A), HN_ULONG_TO_UBASE(0xE2F2CDA7)
    },

    /* 6G.x */
    {
        HN_ULONG_TO_UBASE(0x12D256E1), HN_ULONG_TO_UBASE(0x0DB699E4),
        HN_ULONG_TO_UBASE(0xA526F3F5), HN_ULONG_TO_UBASE(0xCC589514),
        HN_ULONG_TO_UBASE(0xB6F8C073), HN_ULONG_TO_UBASE(0xE7EA29A0),
        HN_ULONG_TO_UBASE(0xADBA7324), HN_ULONG_TO_UBASE(0x50359755),
        HN_ULONG_TO_UBASE(0xE672C579), HN_ULONG_TO_UBASE(0x0FD7FC38),
        HN_ULONG_TO_UBASE(0xF5D93F24), HN_ULONG_TO_UBASE(0xE9FB6A4D)
    },

    /* 6G.y */
    {
        HN_ULONG_TO_UBASE(0x910A0FB5), HN_ULONG_TO_UBASE(0x9103B778),
        HN_ULONG_TO_UBASE(0x1DE052BE), HN_ULONG_TO_UBASE(0x6E7107BD),
        HN_ULONG_TO_UBASE(0xDBDBAE3D), HN_ULONG_TO_UBASE(0x6C24C094),
        HN_ULONG_TO_UBASE(0x66F0CD5A), HN_ULONG_TO_UBASE(0x6F5424A9),
        HN_ULONG_TO_UBASE(0xD171104B), HN_ULONG_TO_UBASE(0xEAB70FFA),
        HN_ULONG_TO_UBASE(0xE51210F9), HN_ULONG_TO_UBASE(0x52AD7C38)
    },

    /* 7G.x */
    {
        HN_ULONG_TO_UBASE(0x1A465EE0), HN_ULONG_TO_UBASE(0x70CB8A4C),
        HN_ULONG_TO_UBASE(0xF8EE3F37), HN_ULONG_TO_UBASE(0xF04BA246),
        HN_ULONG_TO_UBASE(0xC81EE126), HN_ULONG_TO_UBASE(0xD6BEAEB6),
        HN_ULONG_TO_UBASE(0xDC50393C), HN_ULONG_TO_UBASE(0x5FC113E8),
        HN_ULONG_TO_UBASE(0xD094B6A7), HN_ULONG_TO_UBASE(0xD0472DD3),
        HN_ULONG_TO_UBASE(0xDA1C1669), HN_ULONG_TO_UBASE(0xB769B0BE)
    },

    /* 7G.y */
    {
        HN_ULONG_TO_UBASE(0x4157BCA1), HN_ULONG_TO_UBASE(0x772481FA),
        HN_ULONG_TO_UBASE(0x96BEEEC6), HN_ULONG_TO_UBASE(0xDE0AED5E),
        HN_ULONG_TO_UBASE(0x284569C0), HN_ULONG_TO_UBASE(0xB9C04F16),
        HN_ULONG_TO_UBASE(0x8B36D601), HN_ULONG_TO_UBASE(0xA2415911),
        HN_ULONG_TO_UBASE(0xD415E1CA), HN_ULONG_TO_UBASE(0x81D51B7F),
        HN_ULONG_TO_UBASE(0xEBAAD0A2), HN_ULONG_TO_UBASE(0x4FE542B9)
    },

    /* 8G.x */
    {
        HN_ULONG_TO_UBASE(0xA93C10A7), HN_ULONG_TO_UBASE(0xCB610182),
        HN_ULONG_TO_UBASE(0x036AFB3B), HN_ULONG_TO_UBASE(0xBD5059C9),
        HN_ULONG_TO_UBASE(0xFAAC375A), HN_ULONG_TO_UBASE(0xCB538303),
        HN_ULONG_TO_UBASE(0xAC1B02F4), HN_ULONG_TO_UBASE(0xC35A94E6),
        HN_ULONG_TO_UBASE(0x8AE7D58B), HN_ULONG_TO_UBASE(0x8B5D4209),
        HN_ULONG_TO_UBASE(0x004241BD), HN_ULONG_TO_UBASE(0xA8EB2114)
    },

    /* 8G.y */
    {
        HN_ULONG_TO_UBASE(0x262FAC2C), HN_ULONG_TO_UBASE(0xAA3C554A),
        HN_ULONG_TO_UBASE(0x31306B48), HN_ULONG_TO_UBASE(0xC6D2BCDF),
        HN_ULONG_TO_UBASE(0xFAB6BE4D), HN_ULONG_TO_UBASE(0x0ADAE8A9),
        HN_ULONG_TO_UBASE(0x0EA77D12), HN_ULONG_TO_UBASE(0x89B18AED),
        HN_ULONG_TO_UBASE(0xA2675D24), HN_ULONG_TO_UBASE(0x2C4382FF),
        HN_ULONG_TO_UBASE(0x105529BA), HN_ULONG_TO_UBASE(0xABB60CE6)
    },

    /* 9G.x */
    {
        HN_ULONG_TO_UBASE(0xEDF8C996), HN_ULONG_TO_UBASE(0x7FFAF718),
        HN_ULONG_TO_UBASE(0xC58B999A), HN_ULONG_TO_UBASE(0x4EE49986),
        HN_ULONG_TO_UBASE(0xBA5328E9), HN_ULONG_TO_UBASE(0x5FDC0C0F),
        HN_ULONG_TO_UBASE(0x4DE7B0B3), HN_ULONG_TO_UBASE(0x22BB9F3B),
        HN_ULONG_TO_UBASE(0x79A8B5AB), HN_ULONG_TO_UBASE(0x59BDB661),
        HN_ULONG_TO_UBASE(0x5B46960B), HN_ULONG_TO_UBASE(0xA41CEB96)
    },

    /* 9G.y */
    {
        HN_ULONG_TO_UBASE(0x673F565B), HN_ULONG_TO_UBASE(0xF95FD896),
        HN_ULONG_TO_UBASE(0x5546575D), HN_ULONG_TO_UBASE(0x1682F977),
        HN_ULONG_TO_UBASE(0x725E981D), HN_ULONG_TO_UBASE(0x985159D4),
        HN_ULONG_TO_UBASE(0x82EDFF44), HN_ULONG_TO_UBASE(0x2CFE484D),
        HN_ULONG_TO_UBASE(0xE5EFAAD0), HN_ULONG_TO_UBASE(0x785CB625),
        HN_ULONG_TO_UBASE(0x10E28346), HN_ULONG_TO_UBASE(0xC6E94CF8)
    },

    /* 10G.x */
    {
        HN_ULONG_TO_UBASE(0xE79FC953), HN_ULONG_TO_UBASE(0xCFA78FCC),
        HN_ULONG_TO_UBASE(0x62A76A5B), HN_ULONG_TO_UBASE(0xD8503095),
        HN_ULONG_TO_UBASE(0x4AB1FB6E), HN_ULONG_TO_UBASE(0xDF363E09),
        HN_ULONG_TO_UBASE(0xA506B01A), HN_ULONG_TO_UBASE(0x907E97BA),
        HN_ULONG_TO_UBASE(0xA15AF7C1), HN_ULONG_TO_UBASE(0x9BEFB795),
        HN_ULONG_TO_UBASE(0xEAC69987), HN_ULONG_TO_UBASE(0xC7FA7869)
    },

    /* 10G.y */
    {
        HN_ULONG_TO_UBASE(0x1C404FE9), HN_ULONG_TO_UBASE(0xDE4D11B2),
        HN_ULONG_TO_UBASE(0x2E6D0FB8), HN_ULONG_TO_UBASE(0xB0917D3B),
        HN_ULONG_TO_UBASE(0xAEE80FB9), HN_ULONG_TO_UBASE(0xB37CC365),
        HN_ULONG_TO_UBASE(0xF87F9262), HN_ULONG_TO_UBASE(0x306C8470),
        HN_ULONG_TO_UBASE(0x43FC91F8), HN_ULONG_TO_UBASE(0x87519B7F),
        HN_ULONG_TO_UBASE(0x0A61CDF5), HN_ULONG_TO_UBASE(0x9D434DBE)
    },

    /* 11G.x */
    {
        HN_ULONG_TO_UBASE(0xB33139E7), HN_ULONG_TO_UBASE(0x49BF609F),
        HN_ULONG_TO_UBASE(0xAC820A90), HN_ULONG_TO_UBASE(0x60FD2CEB),
        HN_ULONG_TO_UBASE(0x164A20F6), HN_ULONG_TO_UBASE(0xA1344AD0),
        HN_ULONG_TO_UBASE(0xCED42AB2), HN_ULONG_TO_UBASE(0xC8A16564),
        HN_ULONG_TO_UBASE(0x87F81DB5), HN_ULONG_TO_UBASE(0xC778EF62),
        HN_ULONG_TO_UBASE(0xDA05DE0C), HN_ULONG_TO_UBASE(0x81C02C3E)
    },

    /* 11G.y */
    {
        HN_ULONG_TO_UBASE(0xC17D28B9), HN_ULONG_TO_UBASE(0x924D0E64),
        HN_ULONG_TO_UBASE(0x90E31340), HN_ULONG_TO_UBASE(0x8BF310B1),
        HN_ULONG_TO_UBASE(0xA9CE292C), HN_ULONG_TO_UBASE(0x9DDAD413),
        HN_ULONG_TO_UBASE(0xC42F9A8E), HN_ULONG_TO_UBASE(0x46A2A12D),
        HN_ULONG_TO_UBASE(0x69CB4B1D), HN_ULONG_TO_UBASE(0x0C345297),
        HN_ULONG_TO_UBASE(0x4C3EF2D3), HN_ULONG_TO_UBASE(0x1CE0028A)
    },

    /* 12G.x */
    {
        HN_ULONG_TO_UBASE(0x4484249F), HN_ULONG_TO_UBASE(0x7B2EA237),
        HN_ULONG_TO_UBASE(0xDE8D2145), HN_ULONG_TO_UBASE(0xCDC53530),
        HN_ULONG_TO_UBASE(0x225A3DD1), HN_ULONG_TO_UBASE(0x8B6136DD),
        HN_ULONG_TO_UBASE(0x53740EC9), HN_ULONG_TO_UBASE(0xB18E9E73),
        HN_ULONG_TO_UBASE(0x5F27C64A), HN_ULONG_TO_UBASE(0x644E97B7),
        HN_ULONG_TO_UBASE(0xA9CEA0C5), HN_ULONG_TO_UBASE(0xA7208E9D)
    },

    /* 12G.y */
    {
        HN_ULONG_TO_UBASE(0xA48B98EC), HN_ULONG_TO_UBASE(0x6BB544BD),
        HN_ULONG_TO_UBASE(0x57C5F037), HN_ULONG_TO_UBASE(0x1DEBA7F3),
        HN_ULONG_TO_UBASE(0x1F068FB5), HN_ULONG_TO_UBASE(0xBCC87131),
        HN_ULONG_TO_UBASE(0x11CF7C4B), HN_ULONG_TO_UBASE(0xAE719373),
        HN_ULONG_TO_UBASE(0x2CB2EC36), HN_ULONG_TO_UBASE(0x74A95C5B),
        HN_ULONG_TO_UBASE(0x335D77B6), HN_ULONG_TO_UBASE(0x65BE9E50)
    },

    /* 13G.x */
    {
        HN_ULONG_TO_UBASE(0xB2319168), HN_ULONG_TO_UBASE(0xF9A6E7F2),
        HN_ULONG_TO_UBASE(0x51D144A0), HN_ULONG_TO_UBASE(0xEDD5F953),
        HN_ULONG_TO_UBASE(0xAD2AD161), HN_ULONG_TO_UBASE(0x7171C038),
        HN_ULONG_TO_UBASE(0xF7215966), HN_ULONG_TO_UBASE(0x5C01A2BE),
        HN_ULONG_TO_UBASE(0xB978FA06), HN_ULONG_TO_UBASE(0xF696C756),
        HN_ULONG_TO_UBASE(0x6579D248), HN_ULONG_TO_UBASE(0x714398BB)
    },

    /* 13G.y */
    {
        HN_ULONG_TO_UBASE(0xAB1FB325), HN_ULONG_TO_UBASE(0x4ADE5706),
        HN_ULONG_TO_UBASE(0xFF0C1846), HN_ULONG_TO_UBASE(0x818B42B4),
        HN_ULONG_TO_UBASE(0xD6EE937E), HN_ULONG_TO_UBASE(0x7F0C9F34),
        HN_ULONG_TO_UBASE(0x90CD7784), HN_ULONG_TO_UBASE(0x54AC28C5),
        HN_ULONG_TO_UBASE(0xE17F0476), HN_ULONG_TO_UBASE(0x8701F645),
        HN_ULONG_TO_UBASE(0xA4B5D7B8), HN_ULONG_TO_UBASE(0x6545AA51)
    },

    /* 14G.x */
    {
        HN_ULONG_TO_UBASE(0x775226D6), HN_ULONG_TO_UBASE(0x880F880F),
        HN_ULONG_TO_UBASE(0x9CB06473), HN_ULONG_TO_UBASE(0xDF312F51),
        HN_ULONG_TO_UBASE(0x4644DBC4), HN_ULONG_TO_UBASE(0xAAE46B81),
        HN_ULONG_TO_UBASE(0x7654C263), HN_ULONG_TO_UBASE(0x40371DC1),
        HN_ULONG_TO_UBASE(0xBEB9F7AF), HN_ULONG_TO_UBASE(0xC747F85F),
        HN_ULONG_TO_UBASE(0xCC281C52), HN_ULONG_TO_UBASE(0xF4B35B77)
    },

    /* 14G.y */
    {
        HN_ULONG_TO_UBASE(0x8C15E275), HN_ULONG_TO_UBASE(0x28EC4AC0),
        HN_ULONG_TO_UBASE(0xFBF5433B), HN_ULONG_TO_UBASE(0x51537CA3),
        HN_ULONG_TO_UBASE(0xFD212D3F), HN_ULONG_TO_UBASE(0x28CF7BC3),
        HN_ULONG_TO_UBASE(0xE1B6365E), HN_ULONG_TO_UBASE(0xD2D86E75),
        HN_ULONG_TO_UBASE(0x120328CE), HN_ULONG_TO_UBASE(0x047BBCF0),
        HN_ULONG_TO_UBASE(0xF07414D3), HN_ULONG_TO_UBASE(0x33E139EF)
    },

    /* 15G.x */
    {
        HN_ULONG_TO_UBASE(0xCB38E86D), HN_ULONG_TO_UBASE(0xE2A2F4FC),
        HN_ULONG_TO_UBASE(0x5382ED59), HN_ULONG_TO_UBASE(0xCB5357BA),
        HN_ULONG_TO_UBASE(0x1B5076C2), HN_ULONG_TO_UBASE(0x6BE08D5D),
        HN_ULONG_TO_UBASE(0x4D83E11C), HN_ULONG_TO_UBASE(0xC62DF637),
        HN_ULONG_TO_UBASE(0x60969A97), HN_ULONG_TO_UBASE(0xD6958C1E),
        HN_ULONG_TO_UBASE(0x54DBFC48), HN_ULONG_TO_UBASE(0xA49B602C)
    },

    /* 15G.y */
    {
        HN_ULONG_TO_UBASE(0x51914BCA), HN_ULONG_TO_UBASE(0xFB97D2EE),
        HN_ULONG_TO_UBASE(0xAA211719), HN_ULONG_TO_UBASE(0xB4BC64C9),
        HN_ULONG_TO_UBASE(0x00644D20), HN_ULONG_TO_UBASE(0x0ADCD952),
        HN_ULONG_TO_UBASE(0xA75F0046), HN_ULONG_TO_UBASE(0xB8E8CA59),
        HN_ULONG_TO_UBASE(0x17A818F2), HN_ULONG_TO_UBASE(0x9F5E1FE2),
        HN_ULONG_TO_UBASE(0xB5CF54D1), HN_ULONG_TO_UBASE(0x7E1D2F2E)
    },

    /* 16G.x */
    {
        HN_ULONG_TO_UBASE(0x31E76220), HN_ULONG_TO_UBASE(0x3992D2A1),
        HN_ULONG_TO_UBASE(0xFF7003C6), HN_ULONG_TO_UBASE(0x7AD2A60B),
        HN_ULONG_TO_UBASE(0x8F1546BE), HN_ULONG_TO_UBASE(0xDB6A4D39),
        HN_ULONG_TO_UBASE(0xE8A1A2A3), HN_ULONG_TO_UBASE(0xB13228B1),
        HN_ULONG_TO_UBASE(0x993D3A02), HN_ULONG_TO_UBASE(0x1470159C),
        HN_ULONG_TO_UBASE(0x9E9BF4D9), HN_ULONG_TO_UBASE(0x5B4E8B1D)
    },

    /* 16G.y */
    {
        HN_ULONG_TO_UBASE(0x0CD001D1), HN_ULONG_TO_UBASE(0x6FCB85E9),
        HN_ULONG_TO_UBASE(0x3DF1DFE2), HN_ULONG_TO_UBASE(0xE0781A9D),
        HN_ULONG_TO_UBASE(0xC8A2265B), HN_ULONG_TO_UBASE(0xF650285A),
        HN_ULONG_TO_UBASE(0x37A9B579), HN_ULONG_TO_UBASE(0x8727088A),
        HN_ULONG_TO_UBASE(0x22C74609), HN_ULONG_TO_UBASE(0xBB844DF2),
        HN_ULONG_TO_UBASE(0xD5476D33), HN_ULONG_TO_UBASE(0xC3742094)
    },

    /* 17G.x */
    {
        HN_ULONG_TO_UBASE(0x1E060165), HN_ULONG_TO_UBASE(0x79FE2465),
        HN_ULONG_TO_UBASE(0xB6B90F17), HN_ULONG_TO_UBASE(0x5130BDE7),
        HN_ULONG_TO_UBASE(0x853CB459), HN_ULONG_TO_UBASE(0xCE254CFD),
        HN_ULONG_TO_UBASE(0xBA440754), HN_ULONG_TO_UBASE(0xA8782B8E),
        HN_ULONG_TO_UBASE(0xDAF8AA6C), HN_ULONG_TO_UBASE(0x7D81F68F),
        HN_ULONG_TO_UBASE(0x44B8BF68), HN_ULONG_TO_UBASE(0xAA0E19AA)
    },

    /* 17G.y */
    {
        HN_ULONG_TO_UBASE(0x2664A487), HN_ULONG_TO_UBASE(0x6E3EE96F),
        HN_ULONG_TO_UBASE(0x4E9FEA80), HN_ULONG_TO_UBASE(0x8F1B7D25),
        HN_ULONG_TO_UBASE(0x131C050D), HN_ULONG_TO_UBASE(0x7A282A2A),
        HN_ULONG_TO_UBASE(0xCA81498E), HN_ULONG_TO_UBASE(0xD986B357),
        HN_ULONG_TO_UBASE(0x154EC895), HN_ULONG_TO_UBASE(0xC4750753),
        HN_ULONG_TO_UBASE(0xCB3C35A3), HN_ULONG_TO_UBASE(0x65DB0B8A)
    },

    /* 18G.x */
    {
        HN_ULONG_TO_UBASE(0xCE9499EB), HN_ULONG_TO_UBASE(0x4AC90B15),
        HN_ULONG_TO_UBASE(0x91AEE266), HN_ULONG_TO_UBASE(0xBF5777FA),
        HN_ULONG_TO_UBASE(0x4615CE5F), HN_ULONG_TO_UBASE(0xD87272F9),
        HN_ULONG_TO_UBASE(0xFF3C56CE), HN_ULONG_TO_UBASE(0xB92110A3),
        HN_ULONG_TO_UBASE(0xCCA3B289), HN_ULONG_TO_UBASE(0xB327638E),
        HN_ULONG_TO_UBASE(0x3D0A9F44), HN_ULONG_TO_UBASE(0x396B35A9)
    },

    /* 18G.y */
    {
        HN_ULONG_TO_UBASE(0x8AD619EF), HN_ULONG_TO_UBASE(0x0DAD5514),
        HN_ULONG_TO_UBASE(0x3F9AA00A), HN_ULONG_TO_UBASE(0x44242D55),
        HN_ULONG_TO_UBASE(0xB843CDE9), HN_ULONG_TO_UBASE(0xD7E221ED),
        HN_ULONG_TO_UBASE(0x071DDE46), HN_ULONG_TO_UBASE(0xA3C20977),
        HN_ULONG_TO_UBASE(0xA5B4CD7A), HN_ULONG_TO_UBASE(0x16DD93B9),
        HN_ULONG_TO_UBASE(0x021460CA), HN_ULONG_TO_UBASE(0xCCD5DF68)
    },

    /* 19G.x */
    {
        HN_ULONG_TO_UBASE(0x6A570F04), HN_ULONG_TO_UBASE(0x1D21128B),
        HN_ULONG_TO_UBASE(0x394FE427), HN_ULONG_TO_UBASE(0xE917B31C),
        HN_ULONG_TO_UBASE(0x6BA2D13C), HN_ULONG_TO_UBASE(0xC0FE28DE),
        HN_ULONG_TO_UBASE(0x7F08EBA2), HN_ULONG_TO_UBASE(0x2D31795F),
        HN_ULONG_TO_UBASE(0x88492CB7), HN_ULONG_TO_UBASE(0xDABB8957),
        HN_ULONG_TO_UBASE(0xC82A64C1), HN_ULONG_TO_UBASE(0x5B6478B4)
    },

    /* 19G.y */
    {
        HN_ULONG_TO_UBASE(0xCD430E4C), HN_ULONG_TO_UBASE(0x5D14F518),
        HN_ULONG_TO_UBASE(0x217D14F8), HN_ULONG_TO_UBASE(0x552992D1),
        HN_ULONG_TO_UBASE(0x95033367), HN_ULONG_TO_UBASE(0xB38D3C11),
        HN_ULONG_TO_UBASE(0xAE07E0E5), HN_ULONG_TO_UBASE(0xACBB2DDC),
        HN_ULONG_TO_UBASE(0x7B50F818), HN_ULONG_TO_UBASE(0x7093124C),
        HN_ULONG_TO_UBASE(0x7E9CC15B), HN_ULONG_TO_UBASE(0x0AE3337F)
    },

    /* 20G.x */
    {
        HN_ULONG_TO_UBASE(0x428E5500), HN_ULONG_TO_UBASE(0x505F49BA),
        HN_ULONG_TO_UBASE(0x20E83E0D), HN_ULONG_TO_UBASE(0x4FEB246B),
        HN_ULONG_TO_UBASE(0x7C632779), HN_ULONG_TO_UBASE(0x8D18AB7D),
        HN_ULONG_TO_UBASE(0xD299BC0D), HN_ULONG_TO_UBASE(0xFB435379),
        HN_ULONG_TO_UBASE(0x89E66C63), HN_ULONG_TO_UBASE(0xDEA8F23F),
        HN_ULONG_TO_UBASE(0xD93C74A3), HN_ULONG_TO_UBASE(0xDD790987)
    },

    /* 20G.y */
    {
        HN_ULONG_TO_UBASE(0x4B79ADF6), HN_ULONG_TO_UBASE(0xA9AC8F10),
        HN_ULONG_TO_UBASE(0x677F9849), HN_ULONG_TO_UBASE(0xFF4CAA4A),
        HN_ULONG_TO_UBASE(0x80C84B38), HN_ULONG_TO_UBASE(0x4E1BB75C),
        HN_ULONG_TO_UBASE(0x105393D3), HN_ULONG_TO_UBASE(0x52D2575D),
        HN_ULONG_TO_UBASE(0x4569D2D3), HN_ULONG_TO_UBASE(0x4F465F5F),
        HN_ULONG_TO_UBASE(0x41E36869), HN_ULONG_TO_UBASE(0xABAD1376)
    },

    /* 21G.x */
    {
        HN_ULONG_TO_UBASE(0xEB3F72ED), HN_ULONG_TO_UBASE(0xB8566746),
        HN_ULONG_TO_UBASE(0x08E114AE), HN_ULONG_TO_UBASE(0x53316ED1),
        HN_ULONG_TO_UBASE(0x91AEA8C6), HN_ULONG_TO_UBASE(0x45E5B481),
        HN_ULONG_TO_UBASE(0x2857A9D5), HN_ULONG_TO_UBASE(0x73C30BF5),
        HN_ULONG_TO_UBASE(0xFD1F7C82), HN_ULONG_TO_UBASE(0x26DB96AF),
        HN_ULONG_TO_UBASE(0xDF1822B5), HN_ULONG_TO_UBASE(0x8C9010D0)
    },

    /* 21G.y */
    {
        HN_ULONG_TO_UBASE(0x20428D3D), HN_ULONG_TO_UBASE(0x246624AB),
        HN_ULONG_TO_UBASE(0x6A02C7CD), HN_ULONG_TO_UBASE(0xA3A48C9F),
        HN_ULONG_TO_UBASE(0x34CD1BDD), HN_ULONG_TO_UBASE(0x1298B738),
        HN_ULONG_TO_UBASE(0x1B71B3BD), HN_ULONG_TO_UBASE(0x664833BC),
        HN_ULONG_TO_UBASE(0x070A6E08), HN_ULONG_TO_UBASE(0xD9365CD7),
        HN_ULONG_TO_UBASE(0xD610B66B), HN_ULONG_TO_UBASE(0xA44AD979)
    },

    /* 22G.x */
    {
        HN_ULONG_TO_UBASE(0xA6690FC0), HN_ULONG_TO_UBASE(0xCC174EB1),
        HN_ULONG_TO_UBASE(0xC9196D36), HN_ULONG_TO_UBASE(0x5883B4BE),
        HN_ULONG_TO_UBASE(0x4FF222D2), HN_ULONG_TO_UBASE(0x6507BBC3),
        HN_ULONG_TO_UBASE(0x8506370B), HN_ULONG_TO_UBASE(0xF90DAD22),
        HN_ULONG_TO_UBASE(0x235C94E5), HN_ULONG_TO_UBASE(0xD5B17CEC),
        HN_ULONG_TO_UBASE(0x4F1D0704), HN_ULONG_TO_UBASE(0x15C31CD6)
    },

    /* 22G.y */
    {
        HN_ULONG_TO_UBASE(0x83692E96), HN_ULONG_TO_UBASE(0xA45E13E0),
        HN_ULONG_TO_UBASE(0x23632198), HN_ULONG_TO_UBASE(0x30CA9241),
        HN_ULONG_TO_UBASE(0x68C5D526), HN_ULONG_TO_UBASE(0xFB14B0B9),
        HN_ULONG_TO_UBASE(0x53FF8F7F), HN_ULONG_TO_UBASE(0x1CB6AADE),
        HN_ULONG_TO_UBASE(0x9277F031), HN_ULONG_TO_UBASE(0x64B1D3AA),
        HN_ULONG_TO_UBASE(0xB57A14DD), HN_ULONG_TO_UBASE(0x92504AAD)
    },

    /* 23G.x */
    {
        HN_ULONG_TO_UBASE(0x6F824A23), HN_ULONG_TO_UBASE(0xA651A249),
        HN_ULONG_TO_UBASE(0xBC1B0886), HN_ULONG_TO_UBASE(0xABA60A2B),
        HN_ULONG_TO_UBASE(0x67E331A8), HN_ULONG_TO_UBASE(0xC632EF51),
        HN_ULONG_TO_UBASE(0xD3432743), HN_ULONG_TO_UBASE(0x386CAB94),
        HN_ULONG_TO_UBASE(0x24DBDACC), HN_ULONG_TO_UBASE(0x644657CD),
        HN_ULONG_TO_UBASE(0xEA9D8EEB), HN_ULONG_TO_UBASE(0x79BAEFE3)
    },

    /* 23G.y */
    {
        HN_ULONG_TO_UBASE(0x7C0022A9), HN_ULONG_TO_UBASE(0xCE100B59),
        HN_ULONG_TO_UBASE(0xB5552550), HN_ULONG_TO_UBASE(0xC72C67D5),
        HN_ULONG_TO_UBASE(0xC625D47F), HN_ULONG_TO_UBASE(0xCC7C468D),
        HN_ULONG_TO_UBASE(0x43B94872), HN_ULONG_TO_UBASE(0x54376AE2),
        HN_ULONG_TO_UBASE(0xFD91B733), HN_ULONG_TO_UBASE(0x86116D31),
        HN_ULONG_TO_UBASE(0xC07AB981), HN_ULONG_TO_UBASE(0xC33E942E)
    },

    /* 24G.x */
    {
        HN_ULONG_TO_UBASE(0xC1A90C5B), HN_ULONG_TO_UBASE(0x7E0181B9),
        HN_ULONG_TO_UBASE(0xEF64936E), HN_ULONG_TO_UBASE(0x4B2E6511),
        HN_ULONG_TO_UBASE(0xAA71BE85), HN_ULONG_TO_UBASE(0x9187E8D4),
        HN_ULONG_TO_UBASE(0xB683D1DB), HN_ULONG_TO_UBASE(0x9F03A529),
        HN_ULONG_TO_UBASE(0xE63B581E), HN_ULONG_TO_UBASE(0xE9825AAC),
        HN_ULONG_TO_UBASE(0x4B8A03BA), HN_ULONG_TO_UBASE(0x05E6B0A8)
    },

    /* 24G.y */
    {
        HN_ULONG_TO_UBASE(0xF3938636), HN_ULONG_TO_UBASE(0x61907C78),
        HN_ULONG_TO_UBASE(0x7CCADF9D), HN_ULONG_TO_UBASE(0x2DDA27D3),
        HN_ULONG_TO_UBASE(0x9787C6AE), HN_ULONG_TO_UBASE(0x1E7B1E07),
        HN_ULONG_TO_UBASE(0xA645CA8F), HN_ULONG_TO_UBASE(0x6E6A6097),
        HN_ULONG_TO_UBASE(0x3B950770), HN_ULONG_TO_UBASE(0xA152690C),
        HN_ULONG_TO_UBASE(0x80453061), HN_ULONG_TO_UBASE(0xCC813D19)
    },

    /* 25G.x */
    {
        HN_ULONG_TO_UBASE(0xDC9BB565), HN_ULONG_TO_UBASE(0x5026D3E0),
        HN_ULONG_TO_UBASE(0xA41DAC8D), HN_ULONG_TO_UBASE(0x3A345564),
        HN_ULONG_TO_UBASE(0xCF05440B), HN_ULONG_TO_UBASE(0x092B8073),
        HN_ULONG_TO_UBASE(0xE7E95F9A), HN_ULONG_TO_UBASE(0xDE1F971D),
        HN_ULONG_TO_UBASE(0xBCB04838), HN_ULONG_TO_UBASE(0x177D47C6),
        HN_ULONG_TO_UBASE(0x37393D29), HN_ULONG_TO_UBASE(0xB2A0C449)
    },

    /* 25G.y */
    {
        HN_ULONG_TO_UBASE(0xE77340CD), HN_ULONG_TO_UBASE(0x00224C3D),
        HN_ULONG_TO_UBASE(0x6A4E526E), HN_ULONG_TO_UBASE(0x31E37B98),
        HN_ULONG_TO_UBASE(0xBC55A51B), HN_ULONG_TO_UBASE(0xEE98B785),
        HN_ULONG_TO_UBASE(0x091BC664), HN_ULONG_TO_UBASE(0x4ED22126),
        HN_ULONG_TO_UBASE(0x98C7090F), HN_ULONG_TO_UBASE(0x59C178BA),
        HN_ULONG_TO_UBASE(0xA14CE4D5), HN_ULONG_TO_UBASE(0x597FC7F4)
    },

    /* 26G.x */
    {
        HN_ULONG_TO_UBASE(0xA623862F), HN_ULONG_TO_UBASE(0x0DE0AED2),
        HN_ULONG_TO_UBASE(0x49106B56), HN_ULONG_TO_UBASE(0x9195ACAF),
        HN_ULONG_TO_UBASE(0x939A89D1), HN_ULONG_TO_UBASE(0x8703E4AF),
        HN_ULONG_TO_UBASE(0x2AF3BFB2), HN_ULONG_TO_UBASE(0xDA07A303),
        HN_ULONG_TO_UBASE(0xEB51AB60), HN_ULONG_TO_UBASE(0x72817277),
        HN_ULONG_TO_UBASE(0xFA0CB48F), HN_ULONG_TO_UBASE(0x5AEEDCB5)
    },

    /* 26G.y */
    {
        HN_ULONG_TO_UBASE(0x6A386DA2), HN_ULONG_TO_UBASE(0x43E24139),
        HN_ULONG_TO_UBASE(0xA6284E47), HN_ULONG_TO_UBASE(0x09157D8F),
        HN_ULONG_TO_UBASE(0xDCB7B7F6), HN_ULONG_TO_UBASE(0x10D3ABFF),
        HN_ULONG_TO_UBASE(0xC4A4EF51), HN_ULONG_TO_UBASE(0x4FEC85D9),
        HN_ULONG_TO_UBASE(0xE11640B5), HN_ULONG_TO_UBASE(0x6BEFAF87),
        HN_ULONG_TO_UBASE(0x0AFBA91C), HN_ULONG_TO_UBASE(0xB05FF572)
    },

    /* 27G.x */
    {
        HN_ULONG_TO_UBASE(0xFEDF311D), HN_ULONG_TO_UBASE(0x00F305D9),
        HN_ULONG_TO_UBASE(0x6082A9F9), HN_ULONG_TO_UBASE(0x2322592A),
        HN_ULONG_TO_UBASE(0xDFC76F75), HN_ULONG_TO_UBASE(0xF1841C28),
        HN_ULONG_TO_UBASE(0x10AF674E), HN_ULONG_TO_UBASE(0xF0714D17),
        HN_ULONG_TO_UBASE(0xAF895173), HN_ULONG_TO_UBASE(0xCD871803),
        HN_ULONG_TO_UBASE(0x94F5571C), HN_ULONG_TO_UBASE(0x110AB6A9)
    },

    /* 27G.y */
    {
        HN_ULONG_TO_UBASE(0x22D4D124), HN_ULONG_TO_UBASE(0x5AA3B421),
        HN_ULONG_TO_UBASE(0xA2FE7A5F), HN_ULONG_TO_UBASE(0xCB6EB594),
        HN_ULONG_TO_UBASE(0xB6B4AC39), HN_ULONG_TO_UBASE(0xBBE918BA),
        HN_ULONG_TO_UBASE(0x3A31C961), HN_ULONG_TO_UBASE(0x19E5161E),
        HN_ULONG_TO_UBASE(0x3FFFC9CD), HN_ULONG_TO_UBASE(0xC2A7A2CB),
        HN_ULONG_TO_UBASE(0xC67BBAA3), HN_ULONG_TO_UBASE(0x1A0825B1)
    },

    /* 28G.x */
    {
        HN_ULONG_TO_UBASE(0xA02D4BB0), HN_ULONG_TO_UBASE(0x283C9073),
        HN_ULONG_TO_UBASE(0xE05DA927), HN_ULONG_TO_UBASE(0x1C06EEBC),
        HN_ULONG_TO_UBASE(0xA7CE557B), HN_ULONG_TO_UBASE(0xEE920D22),
        HN_ULONG_TO_UBASE(0xF79AEC92), HN_ULONG_TO_UBASE(0xF137A49C),
        HN_ULONG_TO_UBASE(0xF7E0C93D), HN_ULONG_TO_UBASE(0xAC949AA9),
        HN_ULONG_TO_UBASE(0xD2E5D915), HN_ULONG_TO_UBASE(0x1D7481E4)
    },

    /* 28G.y */
    {
        HN_ULONG_TO_UBASE(0x5CBE77D3), HN_ULONG_TO_UBASE(0xAA5A8228),
        HN_ULONG_TO_UBASE(0x128145FD), HN_ULONG_TO_UBASE(0x02459758),
        HN_ULONG_TO_UBASE(0x1BDB11F5), HN_ULONG_TO_UBASE(0xF2096E10),
        HN_ULONG_TO_UBASE(0xA5DC4090), HN_ULONG_TO_UBASE(0x2B4ECB07),
        HN_ULONG_TO_UBASE(0x4C110C19), HN_ULONG_TO_UBASE(0xD335126C),
        HN_ULONG_TO_UBASE(0x27EFAC4C), HN_ULONG_TO_UBASE(0xD1B5960E)
    },

    /* 29G.x */
    {
        HN_ULONG_TO_UBASE(0x77E930E1), HN_ULONG_TO_UBASE(0x3D4100E8),
        HN_ULONG_TO_UBASE(0xADC4C838), HN_ULONG_TO_UBASE(0x0899BAAD),
        HN_ULONG_TO_UBASE(0xF6B3097E), HN_ULONG_TO_UBASE(0x5B64899F),
        HN_ULONG_TO_UBASE(0x2790439D), HN_ULONG_TO_UBASE(0x7C060A89),
        HN_ULONG_TO_UBASE(0x513497C6), HN_ULONG_TO_UBASE(0x40AB25D0),
        HN_ULONG_TO_UBASE(0x202D8833), HN_ULONG_TO_UBASE(0xDFA74FE2)
    },

    /* 29G.y */
    {
        HN_ULONG_TO_UBASE(0x2466F95B), HN_ULONG_TO_UBASE(0x689CCEC5),
        HN_ULONG_TO_UBASE(0xE0B8E88E), HN_ULONG_TO_UBASE(0xE757107A),
        HN_ULONG_TO_UBASE(0x56A78F16), HN_ULONG_TO_UBASE(0x38D0D513),
        HN_ULONG_TO_UBASE(0x5DA9F7C2), HN_ULONG_TO_UBASE(0x47C8301C),
        HN_ULONG_TO_UBASE(0x31956F2B), HN_ULONG_TO_UBASE(0xE8C55CC6),
        HN_ULONG_TO_UBASE(0x0C8D4931), HN_ULONG_TO_UBASE(0x6DA590D6)
    },

    /* 30G.x */
    {
        HN_ULONG_TO_UBASE(0x374E2772), HN_ULONG_TO_UBASE(0xFFEFF253),
        HN_ULONG_TO_UBASE(0x2AFEDDA2), HN_ULONG_TO_UBASE(0xC0132D35),
        HN_ULONG_TO_UBASE(0x6C782F3C), HN_ULONG_TO_UBASE(0xC6211452),
        HN_ULONG_TO_UBASE(0xC98A97E8), HN_ULONG_TO_UBASE(0x7D7F61CD),
        HN_ULONG_TO_UBASE(0x74DB0E01), HN_ULONG_TO_UBASE(0xF0602625),
        HN_ULONG_TO_UBASE(0x5D0D215D), HN_ULONG_TO_UBASE(0x36C1AC6A)
    },

    /* 30G.y */
    {
        HN_ULONG_TO_UBASE(0x59A579DE), HN_ULONG_TO_UBASE(0x88CBE3CF),
        HN_ULONG_TO_UBASE(0xC2C17408), HN_ULONG_TO_UBASE(0x8DDEEC0B),
        HN_ULONG_TO_UBASE(0x034D07D8), HN_ULONG_TO_UBASE(0x6D87FCED),
        HN_ULONG_TO_UBASE(0x656A1F61), HN_ULONG_TO_UBASE(0x9066AFE4),
        HN_ULONG_TO_UBASE(0xFBC82854), HN_ULONG_TO_UBASE(0x758AE55F),
        HN_ULONG_TO_UBASE(0x0F73DFE9), HN_ULONG_TO_UBASE(0x0BC110FA)
    },

    /* 31G.x */
    {
        HN_ULONG_TO_UBASE(0x679A2ABA), HN_ULONG_TO_UBASE(0x96EDF50F),
        HN_ULONG_TO_UBASE(0x7FA01880), HN_ULONG_TO_UBASE(0x31B92B91),
        HN_ULONG_TO_UBASE(0x72495766), HN_ULONG_TO_UBASE(0xFDA047EB),
        HN_ULONG_TO_UBASE(0xCB1299C9), HN_ULONG_TO_UBASE(0xE8C663C5),
        HN_ULONG_TO_UBASE(0x91DBE668), HN_ULONG_TO_UBASE(0x15798146),
        HN_ULONG_TO_UBASE(0x9DA9121C), HN_ULONG_TO_UBASE(0x25E209C5)
    },

    /* 31G.y */
    {
        HN_ULONG_TO_UBASE(0xF69B64DA), HN_ULONG_TO_UBASE(0x9AD033A2),
        HN_ULONG_TO_UBASE(0xD82ADB97), HN_ULONG_TO_UBASE(0x6366E8F3),
        HN_ULONG_TO_UBASE(0xE9103189), HN_ULONG_TO_UBASE(0x96052F28),
        HN_ULONG_TO_UBASE(0x6E6CE744), HN_ULONG_TO_UBASE(0x6C279054),
        HN_ULONG_TO_UBASE(0xFE5D6697), HN_ULONG_TO_UBASE(0xDA53B069),
        HN_ULONG_TO_UBASE(0xDA09FB6A), HN_ULONG_TO_UBASE(0x553200B9)
    }
};
static NX_CRYPTO_CONST HN_UBASE           secp384r1_fixed_points_2e_data[][48 >> HN_SIZE_SHIFT] =
{

    /* 2^e * 1G.x */
    {
        HN_ULONG_TO_UBASE(0xEE69FC0F), HN_ULONG_TO_UBASE(0xCBD89C74),
        HN_ULONG_TO_UBASE(0x777BD97C), HN_ULONG_TO_UBASE(0xCEAA5177),
        HN_ULONG_TO_UBASE(0x03955391), HN_ULONG_TO_UBASE(0xE113574A),
        HN_ULONG_TO_UBASE(0x1B6FCF81), HN_ULONG_TO_UBASE(0x98BB969C),
        HN_ULONG_TO_UBASE(0x64591D15), HN_ULONG_TO_UBASE(0xD1C6BCE5),
        HN_ULONG_TO_UBASE(0xBA330842), HN_ULONG_TO_UBASE(0x1EC28025)
    },

    /* 2^e * 1G.y */
    {
        HN_ULONG_TO_UBASE(0x34829DC8), HN_ULONG_TO_UBASE(0xFD1E4B9E),
        HN_ULONG_TO_UBASE(0x5B2AADDE), HN_ULONG_TO_UBASE(0x1B0007D1),
        HN_ULONG_TO_UBASE(0x25ECF474), HN_ULONG_TO_UBASE(0x391DEE1C),
        HN_ULONG_TO_UBASE(0x70BA0B4D), HN_ULONG_TO_UBASE(0xD41EE6EC),
        HN_ULONG_TO_UBASE(0xA942B396), HN_ULONG_TO_UBASE(0xE3C9E311),
        HN_ULONG_TO_UBASE(0xBA9A279C), HN_ULONG_TO_UBASE(0x7BBC02CD)
    },

    /* 2^e * 2G.x */
    {
        HN_ULONG_TO_UBASE(0x5CA8FAD8), HN_ULONG_TO_UBASE(0x8D12FE42),
        HN_ULONG_TO_UBASE(0xD10970F6), HN_ULONG_TO_UBASE(0x0FBDF6B9),
        HN_ULONG_TO_UBASE(0xC7D54F8E), HN_ULONG_TO_UBASE(0x3EFAC0DA),
        HN_ULONG_TO_UBASE(0x081D4201), HN_ULONG_TO_UBASE(0x78D97625),
        HN_ULONG_TO_UBASE(0xD3724AC0), HN_ULONG_TO_UBASE(0xADBE354F),
        HN_ULONG_TO_UBASE(0xA1C855E6), HN_ULONG_TO_UBASE(0xC7706509)
    },

    /* 2^e * 2G.y */
    {
        HN_ULONG_TO_UBASE(0xDDEE3FA8), HN_ULONG_TO_UBASE(0x3E610D70),
        HN_ULONG_TO_UBASE(0x828A967F), HN_ULONG_TO_UBASE(0xBF575954),
        HN_ULONG_TO_UBASE(0xB53A955E), HN_ULONG_TO_UBASE(0x2AE095F6),
        HN_ULONG_TO_UBASE(0x7B90D88C), HN_ULONG_TO_UBASE(0x56033472),
        HN_ULONG_TO_UBASE(0x8DC0A40A), HN_ULONG_TO_UBASE(0x3C112381),
        HN_ULONG_TO_UBASE(0xD5C067D3), HN_ULONG_TO_UBASE(0x4CFEC7CE)
    },

    /* 2^e * 3G.x */
    {
        HN_ULONG_TO_UBASE(0xAB06C50F), HN_ULONG_TO_UBASE(0x684BEF13),
        HN_ULONG_TO_UBASE(0x0585A276), HN_ULONG_TO_UBASE(0x511B42EE),
        HN_ULONG_TO_UBASE(0x22DD24D6), HN_ULONG_TO_UBASE(0xCA646B6D),
        HN_ULONG_TO_UBASE(0x911A57E9), HN_ULONG_TO_UBASE(0xE7305483),
        HN_ULONG_TO_UBASE(0xB5373F60), HN_ULONG_TO_UBASE(0x39E0E276),
        HN_ULONG_TO_UBASE(0x17DE0D6D), HN_ULONG_TO_UBASE(0xF30CA26E)
    },

    /* 2^e * 3G.y */
    {
        HN_ULONG_TO_UBASE(0x7BC0CB4E), HN_ULONG_TO_UBASE(0xAA4C080E),
        HN_ULONG_TO_UBASE(0xDB7A765E), HN_ULONG_TO_UBASE(0xE09FAEEB),
        HN_ULONG_TO_UBASE(0xCCE5A22E), HN_ULONG_TO_UBASE(0x5C7BADB8),
        HN_ULONG_TO_UBASE(0xCD6B386D), HN_ULONG_TO_UBASE(0x25D55ACC),
        HN_ULONG_TO_UBASE(0xB5311A5D), HN_ULONG_TO_UBASE(0xE58F1A21),
        HN_ULONG_TO_UBASE(0x3406917C), HN_ULONG_TO_UBASE(0x6F4D1442)
    },

    /* 2^e * 4G.x */
    {
        HN_ULONG_TO_UBASE(0x42167978), HN_ULONG_TO_UBASE(0x2488A5E2),
        HN_ULONG_TO_UBASE(0x047CB677), HN_ULONG_TO_UBASE(0x21602406),
        HN_ULONG_TO_UBASE(0x4B533913), HN_ULONG_TO_UBASE(0xBFD5AAD6),
        HN_ULONG_TO_UBASE(0x0B6E7624), HN_ULONG_TO_UBASE(0x21511EC7),
        HN_ULONG_TO_UBASE(0xAC359672), HN_ULONG_TO_UBASE(0xB38E9816),
        HN_ULONG_TO_UBASE(0x58A9BDE3), HN_ULONG_TO_UBASE(0x9B793AF4)
    },

    /* 2^e * 4G.y */
    {
        HN_ULONG_TO_UBASE(0x44999A6D), HN_ULONG_TO_UBASE(0xA8448AF0),
        HN_ULONG_TO_UBASE(0x1F1061FC), HN_ULONG_TO_UBASE(0xEFD02E82),
        HN_ULONG_TO_UBASE(0xDE5F9256), HN_ULONG_TO_UBASE(0x2F04DD33),
        HN_ULONG_TO_UBASE(0x7A770F81), HN_ULONG_TO_UBASE(0x822A5C68),
        HN_ULONG_TO_UBASE(0x5DED11EF), HN_ULONG_TO_UBASE(0xB24C6DEA),
        HN_ULONG_TO_UBASE(0xC0F02EB8), HN_ULONG_TO_UBASE(0xB4744F7D)
    },

    /* 2^e * 5G.x */
    {
        HN_ULONG_TO_UBASE(0x6E2CB973), HN_ULONG_TO_UBASE(0x2654F54E),
        HN_ULONG_TO_UBASE(0x38A0A8D8), HN_ULONG_TO_UBASE(0xB05B48B3),
        HN_ULONG_TO_UBASE(0x6A7E399A), HN_ULONG_TO_UBASE(0xDE415F78),
        HN_ULONG_TO_UBASE(0x479B6AE3), HN_ULONG_TO_UBASE(0x92519281),
        HN_ULONG_TO_UBASE(0x9F1F3538), HN_ULONG_TO_UBASE(0xC1C7BFDF),
        HN_ULONG_TO_UBASE(0xC69C1384), HN_ULONG_TO_UBASE(0xB8BDB872)
    },

    /* 2^e * 5G.y */
    {
        HN_ULONG_TO_UBASE(0xDBF51071), HN_ULONG_TO_UBASE(0xB0378BB9),
        HN_ULONG_TO_UBASE(0x9D5BCB4F), HN_ULONG_TO_UBASE(0x05630F8A),
        HN_ULONG_TO_UBASE(0x28ED6EEF), HN_ULONG_TO_UBASE(0x9C84CB52),
        HN_ULONG_TO_UBASE(0x3C9EDC19), HN_ULONG_TO_UBASE(0x2A55DDC3),
        HN_ULONG_TO_UBASE(0x97139A26), HN_ULONG_TO_UBASE(0x6263DE6A),
        HN_ULONG_TO_UBASE(0x92456DEF), HN_ULONG_TO_UBASE(0x60380E9C)
    },

    /* 2^e * 6G.x */
    {
        HN_ULONG_TO_UBASE(0x945C77EE), HN_ULONG_TO_UBASE(0x42BDB4E4),
        HN_ULONG_TO_UBASE(0xF023E21E), HN_ULONG_TO_UBASE(0x7A225FDB),
        HN_ULONG_TO_UBASE(0x80BDC638), HN_ULONG_TO_UBASE(0x14636D6F),
        HN_ULONG_TO_UBASE(0x0C9CF3AF), HN_ULONG_TO_UBASE(0xEA97539D),
        HN_ULONG_TO_UBASE(0xB1360638), HN_ULONG_TO_UBASE(0x998ABB8C),
        HN_ULONG_TO_UBASE(0x43551856), HN_ULONG_TO_UBASE(0x8CBC90F9)
    },

    /* 2^e * 6G.y */
    {
        HN_ULONG_TO_UBASE(0x69CB4A12), HN_ULONG_TO_UBASE(0x5281BFA5),
        HN_ULONG_TO_UBASE(0xEA8F2EF0), HN_ULONG_TO_UBASE(0x377B1AEE),
        HN_ULONG_TO_UBASE(0x9EC8B98C), HN_ULONG_TO_UBASE(0x1C5A859F),
        HN_ULONG_TO_UBASE(0xA0CBF4E4), HN_ULONG_TO_UBASE(0xEC6CED56),
        HN_ULONG_TO_UBASE(0x93888DF3), HN_ULONG_TO_UBASE(0xA4D50AD8),
        HN_ULONG_TO_UBASE(0x75FB0761), HN_ULONG_TO_UBASE(0x43C729D0)
    },

    /* 2^e * 7G.x */
    {
        HN_ULONG_TO_UBASE(0x6A1D3017), HN_ULONG_TO_UBASE(0x5730E0BE),
        HN_ULONG_TO_UBASE(0x31A9D373), HN_ULONG_TO_UBASE(0xAD6B3AB6),
        HN_ULONG_TO_UBASE(0xA56355D5), HN_ULONG_TO_UBASE(0x67FBFD5C),
        HN_ULONG_TO_UBASE(0xAF532D26), HN_ULONG_TO_UBASE(0x4763B2B7),
        HN_ULONG_TO_UBASE(0x39AC65D3), HN_ULONG_TO_UBASE(0x05683B6E),
        HN_ULONG_TO_UBASE(0x1F39D0E5), HN_ULONG_TO_UBASE(0xD8AB073E)
    },

    /* 2^e * 7G.y */
    {
        HN_ULONG_TO_UBASE(0x5B79D3EA), HN_ULONG_TO_UBASE(0xF2B54247),
        HN_ULONG_TO_UBASE(0x8ECB1473), HN_ULONG_TO_UBASE(0xD6C4FC35),
        HN_ULONG_TO_UBASE(0x6417E5D9), HN_ULONG_TO_UBASE(0x45D01D0B),
        HN_ULONG_TO_UBASE(0xA689C521), HN_ULONG_TO_UBASE(0x8C30A073),
        HN_ULONG_TO_UBASE(0x84DD4B16), HN_ULONG_TO_UBASE(0xE8EB5DE5),
        HN_ULONG_TO_UBASE(0x8EB3E9A1), HN_ULONG_TO_UBASE(0xDCF336FF)
    },

    /* 2^e * 8G.x */
    {
        HN_ULONG_TO_UBASE(0xA8EC4DCB), HN_ULONG_TO_UBASE(0x8FDD9E8B),
        HN_ULONG_TO_UBASE(0x3E9D2CA5), HN_ULONG_TO_UBASE(0x8C7FAFBB),
        HN_ULONG_TO_UBASE(0x986233DC), HN_ULONG_TO_UBASE(0xD902EB1F),
        HN_ULONG_TO_UBASE(0x495E953C), HN_ULONG_TO_UBASE(0xD47D0FDA),
        HN_ULONG_TO_UBASE(0x32E9F30E), HN_ULONG_TO_UBASE(0x000F2E0E),
        HN_ULONG_TO_UBASE(0x226F1883), HN_ULONG_TO_UBASE(0x0B5DB75F)
    },

    /* 2^e * 8G.y */
    {
        HN_ULONG_TO_UBASE(0x9F67269C), HN_ULONG_TO_UBASE(0x7028CE81),
        HN_ULONG_TO_UBASE(0x9C045825), HN_ULONG_TO_UBASE(0xC107538A),
        HN_ULONG_TO_UBASE(0xEC52D696), HN_ULONG_TO_UBASE(0x526F582B),
        HN_ULONG_TO_UBASE(0x35EF4550), HN_ULONG_TO_UBASE(0x99A7FE23),
        HN_ULONG_TO_UBASE(0x4BB5AFFE), HN_ULONG_TO_UBASE(0xD036D89E),
        HN_ULONG_TO_UBASE(0x8BC26250), HN_ULONG_TO_UBASE(0xE17B5092)
    },

    /* 2^e * 9G.x */
    {
        HN_ULONG_TO_UBASE(0x720498AD), HN_ULONG_TO_UBASE(0x79BC9288),
        HN_ULONG_TO_UBASE(0x963268B3), HN_ULONG_TO_UBASE(0xB46D97C4),
        HN_ULONG_TO_UBASE(0x3705CC63), HN_ULONG_TO_UBASE(0x83D6AEE2),
        HN_ULONG_TO_UBASE(0xDC75844D), HN_ULONG_TO_UBASE(0x02EE09A7),
        HN_ULONG_TO_UBASE(0xBCA498D6), HN_ULONG_TO_UBASE(0xFEDD7DFC),
        HN_ULONG_TO_UBASE(0x4C28BF52), HN_ULONG_TO_UBASE(0x3CAC1535)
    },

    /* 2^e * 9G.y */
    {
        HN_ULONG_TO_UBASE(0xECC811C9), HN_ULONG_TO_UBASE(0x4759AB09),
        HN_ULONG_TO_UBASE(0x339928CD), HN_ULONG_TO_UBASE(0x6C13E2D3),
        HN_ULONG_TO_UBASE(0x52EE7B1A), HN_ULONG_TO_UBASE(0x6F1FB566),
        HN_ULONG_TO_UBASE(0x7BE675CC), HN_ULONG_TO_UBASE(0x8ADF88A0),
        HN_ULONG_TO_UBASE(0x33242235), HN_ULONG_TO_UBASE(0x6B4CF3A3),
        HN_ULONG_TO_UBASE(0x5A92C402), HN_ULONG_TO_UBASE(0xC5B7009F)
    },

    /* 2^e * 10G.x */
    {
        HN_ULONG_TO_UBASE(0x996CD141), HN_ULONG_TO_UBASE(0x7B0EED0B),
        HN_ULONG_TO_UBASE(0xAED7BF5F), HN_ULONG_TO_UBASE(0x0A9BB009),
        HN_ULONG_TO_UBASE(0x16B3F8D5), HN_ULONG_TO_UBASE(0xA0D032D1),
        HN_ULONG_TO_UBASE(0xA59A25EA), HN_ULONG_TO_UBASE(0x759FE004),
        HN_ULONG_TO_UBASE(0xFE018B74), HN_ULONG_TO_UBASE(0x0496B4C9),
        HN_ULONG_TO_UBASE(0x2368BE69), HN_ULONG_TO_UBASE(0xD803BA8A)
    },

    /* 2^e * 10G.y */
    {
        HN_ULONG_TO_UBASE(0xE2A6144F), HN_ULONG_TO_UBASE(0xF2302395),
        HN_ULONG_TO_UBASE(0x96779630), HN_ULONG_TO_UBASE(0x8EB14103),
        HN_ULONG_TO_UBASE(0x45C61AFD), HN_ULONG_TO_UBASE(0x1DB4AFE8),
        HN_ULONG_TO_UBASE(0xE3D19276), HN_ULONG_TO_UBASE(0x396F33E4),
        HN_ULONG_TO_UBASE(0x9E810A00), HN_ULONG_TO_UBASE(0x3707E506),
        HN_ULONG_TO_UBASE(0xCF8E1C92), HN_ULONG_TO_UBASE(0x49DAE5B3)
    },

    /* 2^e * 11G.x */
    {
        HN_ULONG_TO_UBASE(0xC2BF6B77), HN_ULONG_TO_UBASE(0x12FB7EEE),
        HN_ULONG_TO_UBASE(0x108568A2), HN_ULONG_TO_UBASE(0x1A71F797),
        HN_ULONG_TO_UBASE(0x1A23CC1F), HN_ULONG_TO_UBASE(0x29677318),
        HN_ULONG_TO_UBASE(0xA0845D27), HN_ULONG_TO_UBASE(0x5C7F10FD),
        HN_ULONG_TO_UBASE(0x33979B89), HN_ULONG_TO_UBASE(0xF5C3C79E),
        HN_ULONG_TO_UBASE(0x3BAB63ED), HN_ULONG_TO_UBASE(0xF75A53EF)
    },

    /* 2^e * 11G.y */
    {
        HN_ULONG_TO_UBASE(0xF3F0C08B), HN_ULONG_TO_UBASE(0x051A54FD),
        HN_ULONG_TO_UBASE(0x91A70E2D), HN_ULONG_TO_UBASE(0xEE7E565D),
        HN_ULONG_TO_UBASE(0x83DCCE34), HN_ULONG_TO_UBASE(0x290AD17C),
        HN_ULONG_TO_UBASE(0xC8BDE478), HN_ULONG_TO_UBASE(0xAE573F1A),
        HN_ULONG_TO_UBASE(0xD5F69236), HN_ULONG_TO_UBASE(0x2D75CE0A),
        HN_ULONG_TO_UBASE(0xE1294F1C), HN_ULONG_TO_UBASE(0xB76D891E)
    },

    /* 2^e * 12G.x */
    {
        HN_ULONG_TO_UBASE(0xF32FB310), HN_ULONG_TO_UBASE(0xAE93A25A),
        HN_ULONG_TO_UBASE(0xE6E47280), HN_ULONG_TO_UBASE(0x6D0CBD1F),
        HN_ULONG_TO_UBASE(0x6C1BA226), HN_ULONG_TO_UBASE(0xA68C6214),
        HN_ULONG_TO_UBASE(0x3EA4307E), HN_ULONG_TO_UBASE(0xCB06F445),
        HN_ULONG_TO_UBASE(0x90B8B6BF), HN_ULONG_TO_UBASE(0xC2964BD1),
        HN_ULONG_TO_UBASE(0xD788E64E), HN_ULONG_TO_UBASE(0x220E5B2E)
    },

    /* 2^e * 12G.y */
    {
        HN_ULONG_TO_UBASE(0x8C2738AA), HN_ULONG_TO_UBASE(0xF0CA4E90),
        HN_ULONG_TO_UBASE(0xFB5BB075), HN_ULONG_TO_UBASE(0xCAD449F3),
        HN_ULONG_TO_UBASE(0x7352836B), HN_ULONG_TO_UBASE(0xCA78A9BA),
        HN_ULONG_TO_UBASE(0x5E3E1251), HN_ULONG_TO_UBASE(0x51DDE73C),
        HN_ULONG_TO_UBASE(0x63B8C3F2), HN_ULONG_TO_UBASE(0xC8604CC4),
        HN_ULONG_TO_UBASE(0x9802EF45), HN_ULONG_TO_UBASE(0xF3DB1991)
    },

    /* 2^e * 13G.x */
    {
        HN_ULONG_TO_UBASE(0x400DDFBF), HN_ULONG_TO_UBASE(0x81C539F3),
        HN_ULONG_TO_UBASE(0xAF546DE4), HN_ULONG_TO_UBASE(0xAA31AD90),
        HN_ULONG_TO_UBASE(0xDACD1FC3), HN_ULONG_TO_UBASE(0xACF6A5C4),
        HN_ULONG_TO_UBASE(0x2E931CA5), HN_ULONG_TO_UBASE(0xEF870E03),
        HN_ULONG_TO_UBASE(0xC3878346), HN_ULONG_TO_UBASE(0x47D59E06),
        HN_ULONG_TO_UBASE(0x6966EBE3), HN_ULONG_TO_UBASE(0x3D7152F0)
    },

    /* 2^e * 13G.y */
    {
        HN_ULONG_TO_UBASE(0x18D57993), HN_ULONG_TO_UBASE(0x650F5A0A),
        HN_ULONG_TO_UBASE(0x2FA7928F), HN_ULONG_TO_UBASE(0xD60D84E3),
        HN_ULONG_TO_UBASE(0x78E647E1), HN_ULONG_TO_UBASE(0x4B6430F8),
        HN_ULONG_TO_UBASE(0x6C0FB1BA), HN_ULONG_TO_UBASE(0x216BEE04),
        HN_ULONG_TO_UBASE(0x243F664F), HN_ULONG_TO_UBASE(0xA6E898A8),
        HN_ULONG_TO_UBASE(0x5DFA6541), HN_ULONG_TO_UBASE(0xADFB9337)
    },

    /* 2^e * 14G.x */
    {
        HN_ULONG_TO_UBASE(0x2AB7BAFC), HN_ULONG_TO_UBASE(0x6F8975D6),
        HN_ULONG_TO_UBASE(0x1657725E), HN_ULONG_TO_UBASE(0x253F30A0),
        HN_ULONG_TO_UBASE(0x9040E877), HN_ULONG_TO_UBASE(0xC352587E),
        HN_ULONG_TO_UBASE(0x9F76979F), HN_ULONG_TO_UBASE(0x49323087),
        HN_ULONG_TO_UBASE(0xC826135A), HN_ULONG_TO_UBASE(0xD1B7844B),
        HN_ULONG_TO_UBASE(0x394F4DAD), HN_ULONG_TO_UBASE(0xC0C380F1)
    },

    /* 2^e * 14G.y */
    {
        HN_ULONG_TO_UBASE(0xDFF69F65), HN_ULONG_TO_UBASE(0xD3C69B8B),
        HN_ULONG_TO_UBASE(0x38F9B216), HN_ULONG_TO_UBASE(0x9A647843),
        HN_ULONG_TO_UBASE(0xBB476DB4), HN_ULONG_TO_UBASE(0x1FF27B96),
        HN_ULONG_TO_UBASE(0xA34F8F45), HN_ULONG_TO_UBASE(0xAF6DF959),
        HN_ULONG_TO_UBASE(0x10134D99), HN_ULONG_TO_UBASE(0x3ACBFCCE),
        HN_ULONG_TO_UBASE(0x1EFD2039), HN_ULONG_TO_UBASE(0xCB8987EE)
    },

    /* 2^e * 15G.x */
    {
        HN_ULONG_TO_UBASE(0xFF6BBF81), HN_ULONG_TO_UBASE(0x076C157A),
        HN_ULONG_TO_UBASE(0x4897A421), HN_ULONG_TO_UBASE(0x196BE03B),
        HN_ULONG_TO_UBASE(0x76DAD52C), HN_ULONG_TO_UBASE(0xCAF25CA7),
        HN_ULONG_TO_UBASE(0xB324D2C0), HN_ULONG_TO_UBASE(0x79F8B09B),
        HN_ULONG_TO_UBASE(0xE3B929EB), HN_ULONG_TO_UBASE(0xE2989513),
        HN_ULONG_TO_UBASE(0x62CF6433), HN_ULONG_TO_UBASE(0xDC6145F6)
    },

    /* 2^e * 15G.y */
    {
        HN_ULONG_TO_UBASE(0x18F5F266), HN_ULONG_TO_UBASE(0x4D338705),
        HN_ULONG_TO_UBASE(0xAAA279F3), HN_ULONG_TO_UBASE(0x2AB679D7),
        HN_ULONG_TO_UBASE(0x16F35C76), HN_ULONG_TO_UBASE(0x6D7F11D5),
        HN_ULONG_TO_UBASE(0xAC9B005B), HN_ULONG_TO_UBASE(0x104DCC7F),
        HN_ULONG_TO_UBASE(0x9FA79809), HN_ULONG_TO_UBASE(0x4131F098),
        HN_ULONG_TO_UBASE(0x590FB907), HN_ULONG_TO_UBASE(0x2DA645E8)
    },

    /* 2^e * 16G.x */
    {
        HN_ULONG_TO_UBASE(0x7DE8FBC8), HN_ULONG_TO_UBASE(0x8C56016E),
        HN_ULONG_TO_UBASE(0x109811E4), HN_ULONG_TO_UBASE(0x12F4B01D),
        HN_ULONG_TO_UBASE(0x2E8E9787), HN_ULONG_TO_UBASE(0x5C1740FF),
        HN_ULONG_TO_UBASE(0xFD1A9591), HN_ULONG_TO_UBASE(0x1EF71AAC),
        HN_ULONG_TO_UBASE(0xD06BD8B5), HN_ULONG_TO_UBASE(0xBF0993F9),
        HN_ULONG_TO_UBASE(0x64559C30), HN_ULONG_TO_UBASE(0x8F040E83)
    },

    /* 2^e * 16G.y */
    {
        HN_ULONG_TO_UBASE(0xB19DAAFD), HN_ULONG_TO_UBASE(0xF3CFF2FA),
        HN_ULONG_TO_UBASE(0x7A74F7B4), HN_ULONG_TO_UBASE(0x6A2AB770),
        HN_ULONG_TO_UBASE(0x7C73FE42), HN_ULONG_TO_UBASE(0x59F8ED55),
        HN_ULONG_TO_UBASE(0xA1B176C4), HN_ULONG_TO_UBASE(0xC8EC72C9),
        HN_ULONG_TO_UBASE(0x58904740), HN_ULONG_TO_UBASE(0xDE4FBA38),
        HN_ULONG_TO_UBASE(0xBF8F9971), HN_ULONG_TO_UBASE(0xB4044B0C)
    },

    /* 2^e * 17G.x */
    {
        HN_ULONG_TO_UBASE(0x9D783476), HN_ULONG_TO_UBASE(0x04FF868C),
        HN_ULONG_TO_UBASE(0x9A2BD397), HN_ULONG_TO_UBASE(0xD6847F7C),
        HN_ULONG_TO_UBASE(0xB1D0DAE0), HN_ULONG_TO_UBASE(0xFA2FE102),
        HN_ULONG_TO_UBASE(0xBCA32C6D), HN_ULONG_TO_UBASE(0xF257A5D1),
        HN_ULONG_TO_UBASE(0x31CD5BB5), HN_ULONG_TO_UBASE(0xFF5FB245),
        HN_ULONG_TO_UBASE(0x89EE180E), HN_ULONG_TO_UBASE(0x0BDC32FE)
    },

    /* 2^e * 17G.y */
    {
        HN_ULONG_TO_UBASE(0x1D5EE790), HN_ULONG_TO_UBASE(0x6B748C7B),
        HN_ULONG_TO_UBASE(0xC2EE06C3), HN_ULONG_TO_UBASE(0xB5975608),
        HN_ULONG_TO_UBASE(0xF8E08565), HN_ULONG_TO_UBASE(0x03D3CEC2),
        HN_ULONG_TO_UBASE(0xC029274A), HN_ULONG_TO_UBASE(0x3C174FF4),
        HN_ULONG_TO_UBASE(0x310CE0A9), HN_ULONG_TO_UBASE(0xE6EF58F5),
        HN_ULONG_TO_UBASE(0x581EC828), HN_ULONG_TO_UBASE(0xDD5A6C7D)
    },

    /* 2^e * 18G.x */
    {
        HN_ULONG_TO_UBASE(0x3F29C6DA), HN_ULONG_TO_UBASE(0x91699928),
        HN_ULONG_TO_UBASE(0xA27FA89B), HN_ULONG_TO_UBASE(0x6307CC33),
        HN_ULONG_TO_UBASE(0x6F8E4D58), HN_ULONG_TO_UBASE(0x4961F6C1),
        HN_ULONG_TO_UBASE(0xCCE0050E), HN_ULONG_TO_UBASE(0x6D769DBE),
        HN_ULONG_TO_UBASE(0x4C77D1CF), HN_ULONG_TO_UBASE(0x235F1A63),
        HN_ULONG_TO_UBASE(0x51CA6E0A), HN_ULONG_TO_UBASE(0xABCD2FF6)
    },

    /* 2^e * 18G.y */
    {
        HN_ULONG_TO_UBASE(0x0517D9FD), HN_ULONG_TO_UBASE(0x19B7C27A),
        HN_ULONG_TO_UBASE(0x1E14A80E), HN_ULONG_TO_UBASE(0xAFC922EB),
        HN_ULONG_TO_UBASE(0x7B486A4A), HN_ULONG_TO_UBASE(0xA4544CA6),
        HN_ULONG_TO_UBASE(0x15117677), HN_ULONG_TO_UBASE(0x64B1D030),
        HN_ULONG_TO_UBASE(0xE8860718), HN_ULONG_TO_UBASE(0x4A5354BD),
        HN_ULONG_TO_UBASE(0x95F6AC77), HN_ULONG_TO_UBASE(0x63A83299)
    },

    /* 2^e * 19G.x */
    {
        HN_ULONG_TO_UBASE(0xB31B0A97), HN_ULONG_TO_UBASE(0xEA0E51B0),
        HN_ULONG_TO_UBASE(0x6A15D6F4), HN_ULONG_TO_UBASE(0xB6B52443),
        HN_ULONG_TO_UBASE(0x30C5E5D2), HN_ULONG_TO_UBASE(0xEE59B33D),
        HN_ULONG_TO_UBASE(0x0AB60DCB), HN_ULONG_TO_UBASE(0x862AE200),
        HN_ULONG_TO_UBASE(0xF83AE53A), HN_ULONG_TO_UBASE(0x2DFF95EE),
        HN_ULONG_TO_UBASE(0xABAC2A31), HN_ULONG_TO_UBASE(0xDEE75AF8)
    },

    /* 2^e * 19G.y */
    {
        HN_ULONG_TO_UBASE(0xB3FE383A), HN_ULONG_TO_UBASE(0x4BDFE8E0),
        HN_ULONG_TO_UBASE(0x4154109D), HN_ULONG_TO_UBASE(0x8C95C6C4),
        HN_ULONG_TO_UBASE(0x6CF2E9A4), HN_ULONG_TO_UBASE(0xB886A668),
        HN_ULONG_TO_UBASE(0x9CC66DC0), HN_ULONG_TO_UBASE(0xCF2E1362),
        HN_ULONG_TO_UBASE(0x4B8EF1B8), HN_ULONG_TO_UBASE(0xBB665A34),
        HN_ULONG_TO_UBASE(0xA110EEDC), HN_ULONG_TO_UBASE(0x3985801B)
    },

    /* 2^e * 20G.x */
    {
        HN_ULONG_TO_UBASE(0x7676635D), HN_ULONG_TO_UBASE(0xB5EAAD66),
        HN_ULONG_TO_UBASE(0x4B15B8A3), HN_ULONG_TO_UBASE(0xCE2AAE2B),
        HN_ULONG_TO_UBASE(0xBAE4F3C0), HN_ULONG_TO_UBASE(0x0DA04CBE),
        HN_ULONG_TO_UBASE(0xF81B1920), HN_ULONG_TO_UBASE(0x0763CCAA),
        HN_ULONG_TO_UBASE(0xE98897D5), HN_ULONG_TO_UBASE(0xC962D4F0),
        HN_ULONG_TO_UBASE(0x19FDAA33), HN_ULONG_TO_UBASE(0xB1354D28)
    },

    /* 2^e * 20G.y */
    {
        HN_ULONG_TO_UBASE(0x655C2856), HN_ULONG_TO_UBASE(0x032431E6),
        HN_ULONG_TO_UBASE(0x667D4DE5), HN_ULONG_TO_UBASE(0xBE185E46),
        HN_ULONG_TO_UBASE(0xD961220E), HN_ULONG_TO_UBASE(0xE3FAB495),
        HN_ULONG_TO_UBASE(0x1132AA7A), HN_ULONG_TO_UBASE(0xCC1889B7),
        HN_ULONG_TO_UBASE(0xFA9ED4A2), HN_ULONG_TO_UBASE(0x13699414),
        HN_ULONG_TO_UBASE(0x9D9542D6), HN_ULONG_TO_UBASE(0x54A94741)
    },

    /* 2^e * 21G.x */
    {
        HN_ULONG_TO_UBASE(0x01DDCB22), HN_ULONG_TO_UBASE(0xA6CBEE20),
        HN_ULONG_TO_UBASE(0xD172C1FA), HN_ULONG_TO_UBASE(0x4E514181),
        HN_ULONG_TO_UBASE(0x94A30E74), HN_ULONG_TO_UBASE(0xE96C0261),
        HN_ULONG_TO_UBASE(0x71DD3E3B), HN_ULONG_TO_UBASE(0x50A70687),
        HN_ULONG_TO_UBASE(0x48D71A28), HN_ULONG_TO_UBASE(0x97315C33),
        HN_ULONG_TO_UBASE(0x17B6D0A0), HN_ULONG_TO_UBASE(0x9DCDDB2A)
    },

    /* 2^e * 21G.y */
    {
        HN_ULONG_TO_UBASE(0x0B489DA0), HN_ULONG_TO_UBASE(0xA8228D75),
        HN_ULONG_TO_UBASE(0xCA3528F9), HN_ULONG_TO_UBASE(0x7D762EB3),
        HN_ULONG_TO_UBASE(0x35BE5882), HN_ULONG_TO_UBASE(0x321499B4),
        HN_ULONG_TO_UBASE(0x590B4DEC), HN_ULONG_TO_UBASE(0x1C9287AC),
        HN_ULONG_TO_UBASE(0x7F251108), HN_ULONG_TO_UBASE(0x3E9ADC31),
        HN_ULONG_TO_UBASE(0xE916EEC7), HN_ULONG_TO_UBASE(0x2DCF7C91)
    },

    /* 2^e * 22G.x */
    {
        HN_ULONG_TO_UBASE(0x80E88DDF), HN_ULONG_TO_UBASE(0x36A44B50),
        HN_ULONG_TO_UBASE(0x481B9AFD), HN_ULONG_TO_UBASE(0x1BD1C020),
        HN_ULONG_TO_UBASE(0x7A255175), HN_ULONG_TO_UBASE(0xCBF3BC20),
        HN_ULONG_TO_UBASE(0x7983D6B8), HN_ULONG_TO_UBASE(0x78D0E419),
        HN_ULONG_TO_UBASE(0xD30DF778), HN_ULONG_TO_UBASE(0x29D268FF),
        HN_ULONG_TO_UBASE(0xBEBF77CB), HN_ULONG_TO_UBASE(0x8DABAEE8)
    },

    /* 2^e * 22G.y */
    {
        HN_ULONG_TO_UBASE(0xB7901C46), HN_ULONG_TO_UBASE(0xB5E736D2),
        HN_ULONG_TO_UBASE(0x30A0870A), HN_ULONG_TO_UBASE(0xFA4744B6),
        HN_ULONG_TO_UBASE(0xFDC9AD1B), HN_ULONG_TO_UBASE(0x938AED88),
        HN_ULONG_TO_UBASE(0x833D2D39), HN_ULONG_TO_UBASE(0xED2240D2),
        HN_ULONG_TO_UBASE(0x2CA846A5), HN_ULONG_TO_UBASE(0x516BEA26),
        HN_ULONG_TO_UBASE(0x35D43D9F), HN_ULONG_TO_UBASE(0x725F74E2)
    },

    /* 2^e * 23G.x */
    {
        HN_ULONG_TO_UBASE(0x1FD171E3), HN_ULONG_TO_UBASE(0x4E74393D),
        HN_ULONG_TO_UBASE(0x47D2E77C), HN_ULONG_TO_UBASE(0x4CD9B12C),
        HN_ULONG_TO_UBASE(0xEF0D7390), HN_ULONG_TO_UBASE(0xC2D9684D),
        HN_ULONG_TO_UBASE(0x407D76F2), HN_ULONG_TO_UBASE(0x44EDBD18),
        HN_ULONG_TO_UBASE(0x5086202D), HN_ULONG_TO_UBASE(0x18FB339E),
        HN_ULONG_TO_UBASE(0x92AD71C1), HN_ULONG_TO_UBASE(0xA3D55B78)
    },

    /* 2^e * 23G.y */
    {
        HN_ULONG_TO_UBASE(0xEC7856C9), HN_ULONG_TO_UBASE(0x477E73FC),
        HN_ULONG_TO_UBASE(0x8BAD1445), HN_ULONG_TO_UBASE(0xD675A733),
        HN_ULONG_TO_UBASE(0xE01B375D), HN_ULONG_TO_UBASE(0xB6B60D37),
        HN_ULONG_TO_UBASE(0x433B0AD0), HN_ULONG_TO_UBASE(0xB868E2D6),
        HN_ULONG_TO_UBASE(0x19174889), HN_ULONG_TO_UBASE(0x6D95BDB9),
        HN_ULONG_TO_UBASE(0xF49BE335), HN_ULONG_TO_UBASE(0xD86B9FB2)
    },

    /* 2^e * 24G.x */
    {
        HN_ULONG_TO_UBASE(0xB29FCD8E), HN_ULONG_TO_UBASE(0x2BC2ED08),
        HN_ULONG_TO_UBASE(0x3B050AA0), HN_ULONG_TO_UBASE(0x0E051889),
        HN_ULONG_TO_UBASE(0x0C9E1B7D), HN_ULONG_TO_UBASE(0x16B3DE55),
        HN_ULONG_TO_UBASE(0x8AE2DC7A), HN_ULONG_TO_UBASE(0xB9D4C6FA),
        HN_ULONG_TO_UBASE(0xC9417C80), HN_ULONG_TO_UBASE(0xF9067836),
        HN_ULONG_TO_UBASE(0x60B951D6), HN_ULONG_TO_UBASE(0x7ADE26C9)
    },

    /* 2^e * 24G.y */
    {
        HN_ULONG_TO_UBASE(0xF93A334E), HN_ULONG_TO_UBASE(0x8F9A62FD),
        HN_ULONG_TO_UBASE(0x7D0A4596), HN_ULONG_TO_UBASE(0x82A01186),
        HN_ULONG_TO_UBASE(0x56393DBB), HN_ULONG_TO_UBASE(0xB17FD21F),
        HN_ULONG_TO_UBASE(0x3867C182), HN_ULONG_TO_UBASE(0xECEFFA73),
        HN_ULONG_TO_UBASE(0xCC325FB4), HN_ULONG_TO_UBASE(0xB58C6EFD),
        HN_ULONG_TO_UBASE(0x51305B68), HN_ULONG_TO_UBASE(0x2762D890)
    },

    /* 2^e * 25G.x */
    {
        HN_ULONG_TO_UBASE(0xD0BACDDF), HN_ULONG_TO_UBASE(0x5EC458D0),
        HN_ULONG_TO_UBASE(0x8B6C0C20), HN_ULONG_TO_UBASE(0x5F00DC55),
        HN_ULONG_TO_UBASE(0x61392B6A), HN_ULONG_TO_UBASE(0x5D0F4AAD),
        HN_ULONG_TO_UBASE(0x4B1B3EC0), HN_ULONG_TO_UBASE(0x83AECE93),
        HN_ULONG_TO_UBASE(0x6FF068ED), HN_ULONG_TO_UBASE(0x6467B444),
        HN_ULONG_TO_UBASE(0x860EFF5E), HN_ULONG_TO_UBASE(0x7DEB632F)
    },

    /* 2^e * 25G.y */
    {
        HN_ULONG_TO_UBASE(0x01BC13A2), HN_ULONG_TO_UBASE(0x7F98C795),
        HN_ULONG_TO_UBASE(0x144654F6), HN_ULONG_TO_UBASE(0xA17A3AE0),
        HN_ULONG_TO_UBASE(0x1F338F77), HN_ULONG_TO_UBASE(0xFB37245A),
        HN_ULONG_TO_UBASE(0x02980347), HN_ULONG_TO_UBASE(0xD2B78CF7),
        HN_ULONG_TO_UBASE(0xBDC17EA4), HN_ULONG_TO_UBASE(0x2F290905),
        HN_ULONG_TO_UBASE(0x3C0FBD8B), HN_ULONG_TO_UBASE(0x50BCE7FD)
    },

    /* 2^e * 26G.x */
    {
        HN_ULONG_TO_UBASE(0x2DE236DE), HN_ULONG_TO_UBASE(0x1468EECF),
        HN_ULONG_TO_UBASE(0x012467C8), HN_ULONG_TO_UBASE(0xF10BE7B5),
        HN_ULONG_TO_UBASE(0x9F35467C), HN_ULONG_TO_UBASE(0x9A099B7E),
        HN_ULONG_TO_UBASE(0xCF3D3593), HN_ULONG_TO_UBASE(0x77A6D7EE),
        HN_ULONG_TO_UBASE(0x9938334D), HN_ULONG_TO_UBASE(0xE12E223E),
        HN_ULONG_TO_UBASE(0x1111122A), HN_ULONG_TO_UBASE(0x612A9319)
    },

    /* 2^e * 26G.y */
    {
        HN_ULONG_TO_UBASE(0xD9F39234), HN_ULONG_TO_UBASE(0xBB8EDE43),
        HN_ULONG_TO_UBASE(0x6B69E865), HN_ULONG_TO_UBASE(0xF47ECAA8),
        HN_ULONG_TO_UBASE(0xF2CC279F), HN_ULONG_TO_UBASE(0x309D4019),
        HN_ULONG_TO_UBASE(0xF92A1732), HN_ULONG_TO_UBASE(0xF04ED535),
        HN_ULONG_TO_UBASE(0x556B0C2F), HN_ULONG_TO_UBASE(0x41378845),
        HN_ULONG_TO_UBASE(0x343A850B), HN_ULONG_TO_UBASE(0xE478B07C)
    },

    /* 2^e * 27G.x */
    {
        HN_ULONG_TO_UBASE(0x67040795), HN_ULONG_TO_UBASE(0x99BF7824),
        HN_ULONG_TO_UBASE(0xE0D09657), HN_ULONG_TO_UBASE(0xB3174F6D),
        HN_ULONG_TO_UBASE(0x9BD48F08), HN_ULONG_TO_UBASE(0x0AA62BCD),
        HN_ULONG_TO_UBASE(0x0080F1C3), HN_ULONG_TO_UBASE(0xE14CF302),
        HN_ULONG_TO_UBASE(0x1235280E), HN_ULONG_TO_UBASE(0x1C6733C1),
        HN_ULONG_TO_UBASE(0xB70F9435), HN_ULONG_TO_UBASE(0xCC2D0011)
    },

    /* 2^e * 27G.y */
    {
        HN_ULONG_TO_UBASE(0x15384AC2), HN_ULONG_TO_UBASE(0xD269F1D1),
        HN_ULONG_TO_UBASE(0xD2B73477), HN_ULONG_TO_UBASE(0x9AD2E394),
        HN_ULONG_TO_UBASE(0xDD44D743), HN_ULONG_TO_UBASE(0x5FC362AE),
        HN_ULONG_TO_UBASE(0xB5DEA0B0), HN_ULONG_TO_UBASE(0xF0D7E109),
        HN_ULONG_TO_UBASE(0xCE689EC7), HN_ULONG_TO_UBASE(0xC9FC044C),
        HN_ULONG_TO_UBASE(0x1B791918), HN_ULONG_TO_UBASE(0x9B2A8EE4)
    },

    /* 2^e * 28G.x */
    {
        HN_ULONG_TO_UBASE(0xA7F3896D), HN_ULONG_TO_UBASE(0xB4EB87E2),
        HN_ULONG_TO_UBASE(0xAD9B5B2C), HN_ULONG_TO_UBASE(0x36E99DDD),
        HN_ULONG_TO_UBASE(0xDFB6B879), HN_ULONG_TO_UBASE(0x94DB8243),
        HN_ULONG_TO_UBASE(0x8F5DE0A0), HN_ULONG_TO_UBASE(0x4F007E0D),
        HN_ULONG_TO_UBASE(0x159DE404), HN_ULONG_TO_UBASE(0x2181FD84),
        HN_ULONG_TO_UBASE(0xE9002836), HN_ULONG_TO_UBASE(0x7F799B46)
    },

    /* 2^e * 28G.y */
    {
        HN_ULONG_TO_UBASE(0xEFBB502A), HN_ULONG_TO_UBASE(0x4EF02E02),
        HN_ULONG_TO_UBASE(0x01FD6807), HN_ULONG_TO_UBASE(0xAA8A2900),
        HN_ULONG_TO_UBASE(0x2B17884B), HN_ULONG_TO_UBASE(0xD5118793),
        HN_ULONG_TO_UBASE(0x4CE8660A), HN_ULONG_TO_UBASE(0x0BFA646A),
        HN_ULONG_TO_UBASE(0xF04CB9E8), HN_ULONG_TO_UBASE(0x9548772B),
        HN_ULONG_TO_UBASE(0x47ACA66F), HN_ULONG_TO_UBASE(0xAED85F62)
    },

    /* 2^e * 29G.x */
    {
        HN_ULONG_TO_UBASE(0xEDCBB430), HN_ULONG_TO_UBASE(0x1CD6A960),
        HN_ULONG_TO_UBASE(0xCB37B09C), HN_ULONG_TO_UBASE(0xF3CC47D6),
        HN_ULONG_TO_UBASE(0x542C040B), HN_ULONG_TO_UBASE(0x227DAC36),
        HN_ULONG_TO_UBASE(0xE6338069), HN_ULONG_TO_UBASE(0x2B995E24),
        HN_ULONG_TO_UBASE(0xAF0D202D), HN_ULONG_TO_UBASE(0x7DBF5606),
        HN_ULONG_TO_UBASE(0xD464B0C1), HN_ULONG_TO_UBASE(0xFD6E2B95)
    },

    /* 2^e * 29G.y */
    {
        HN_ULONG_TO_UBASE(0x0589B2B7), HN_ULONG_TO_UBASE(0x19D75625),
        HN_ULONG_TO_UBASE(0x1B7E0405), HN_ULONG_TO_UBASE(0xA88ECA19),
        HN_ULONG_TO_UBASE(0x5AA169B6), HN_ULONG_TO_UBASE(0x39409D88),
        HN_ULONG_TO_UBASE(0x370BCAD0), HN_ULONG_TO_UBASE(0xD266FC24),
        HN_ULONG_TO_UBASE(0x6E648420), HN_ULONG_TO_UBASE(0x96903090),
        HN_ULONG_TO_UBASE(0xDE5D854B), HN_ULONG_TO_UBASE(0xA02242F9)
    },

    /* 2^e * 30G.x */
    {
        HN_ULONG_TO_UBASE(0x027E096E), HN_ULONG_TO_UBASE(0x8C9E8282),
        HN_ULONG_TO_UBASE(0x0B462AC5), HN_ULONG_TO_UBASE(0x052D0556),
        HN_ULONG_TO_UBASE(0xCECC307A), HN_ULONG_TO_UBASE(0x2CE9D135),
        HN_ULONG_TO_UBASE(0xB76CEDD3), HN_ULONG_TO_UBASE(0x25F6B914),
        HN_ULONG_TO_UBASE(0xD9259B0C), HN_ULONG_TO_UBASE(0xACC0839D),
        HN_ULONG_TO_UBASE(0x5652D512), HN_ULONG_TO_UBASE(0xAA8C8841)
    },

    /* 2^e * 30G.y */
    {
        HN_ULONG_TO_UBASE(0xF8F0ECF7), HN_ULONG_TO_UBASE(0xAD043205),
        HN_ULONG_TO_UBASE(0x9594482F), HN_ULONG_TO_UBASE(0x81088B3C),
        HN_ULONG_TO_UBASE(0x5DE69DF0), HN_ULONG_TO_UBASE(0x9A27B905),
        HN_ULONG_TO_UBASE(0x0018C17B), HN_ULONG_TO_UBASE(0xC1576503),
        HN_ULONG_TO_UBASE(0x04511B87), HN_ULONG_TO_UBASE(0xAD25B6AB),
        HN_ULONG_TO_UBASE(0x8A92B28D), HN_ULONG_TO_UBASE(0xA5624218)
    },

    /* 2^e * 31G.x */
    {
        HN_ULONG_TO_UBASE(0x448320B7), HN_ULONG_TO_UBASE(0xB0AA0B05),
        HN_ULONG_TO_UBASE(0x543BA441), HN_ULONG_TO_UBASE(0x3A5F5043),
        HN_ULONG_TO_UBASE(0x7047E54B), HN_ULONG_TO_UBASE(0xFACABDF4),
        HN_ULONG_TO_UBASE(0x5A1197AD), HN_ULONG_TO_UBASE(0x48DD13E1),
        HN_ULONG_TO_UBASE(0xFB00302E), HN_ULONG_TO_UBASE(0xDBB3B5C4),
        HN_ULONG_TO_UBASE(0x6E938D95), HN_ULONG_TO_UBASE(0x297E0535)
    },

    /* 2^e * 31G.y */
    {
        HN_ULONG_TO_UBASE(0xEF8773AB), HN_ULONG_TO_UBASE(0x0B6C2CA0),
        HN_ULONG_TO_UBASE(0x71EA0C8E), HN_ULONG_TO_UBASE(0x6296D4D6),
        HN_ULONG_TO_UBASE(0x7E448A7A), HN_ULONG_TO_UBASE(0x49BD8645),
        HN_ULONG_TO_UBASE(0x61C7CCBF), HN_ULONG_TO_UBASE(0xF8D69BEB),
        HN_ULONG_TO_UBASE(0xC1E64156), HN_ULONG_TO_UBASE(0xE60FEADB),
        HN_ULONG_TO_UBASE(0x57BBD1CF), HN_ULONG_TO_UBASE(0x4CD395EE)
    }
};
static NX_CRYPTO_CONST NX_CRYPTO_EC_POINT secp384r1_fixed_points_array[] =
{

    /* 2G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[0],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[1],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 3G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[2],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[3],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 4G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[4],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[5],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 5G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[6],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[7],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 6G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[8],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[9],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 7G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[10],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[11],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 8G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[12],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[13],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 9G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[14],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[15],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 10G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[16],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[17],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 11G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[18],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[19],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 12G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[20],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[21],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 13G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[22],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[23],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 14G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[24],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[25],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 15G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[26],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[27],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 16G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[28],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[29],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 17G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[30],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[31],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 18G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[32],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[33],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 19G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[34],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[35],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 20G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[36],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[37],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 21G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[38],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[39],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 22G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[40],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[41],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 23G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[42],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[43],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 24G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[44],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[45],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 25G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[46],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[47],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 26G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[48],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[49],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 27G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[50],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[51],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 28G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[52],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[53],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 29G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[54],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[55],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 30G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[56],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[57],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 31G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[58],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_data[59],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    }
};
static NX_CRYPTO_CONST NX_CRYPTO_EC_POINT secp384r1_fixed_points_2e_array[] =
{

    /* 2^e * 1G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[0],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[1],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 2G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[2],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[3],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 3G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[4],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[5],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 4G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[6],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[7],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 5G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[8],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[9],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 6G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[10],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[11],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 7G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[12],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[13],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 8G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[14],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[15],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 9G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[16],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[17],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 10G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[18],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[19],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 11G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[20],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[21],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 12G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[22],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[23],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 13G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[24],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[25],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 14G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[26],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[27],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 15G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[28],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[29],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 16G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[30],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[31],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 17G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[32],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[33],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 18G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[34],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[35],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 19G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[36],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[37],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 20G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[38],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[39],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 21G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[40],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[41],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 22G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[42],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[43],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 23G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[44],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[45],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 24G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[46],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[47],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 25G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[48],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[49],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 26G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[50],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[51],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 27G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[52],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[53],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 28G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[54],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[55],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 29G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[56],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[57],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 30G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[58],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[59],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 31G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[60],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp384r1_fixed_points_2e_data[61],
            48 >> HN_SIZE_SHIFT, 48, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    }
};


NX_CRYPTO_CONST NX_CRYPTO_EC_FIXED_POINTS _nx_crypto_ec_secp384r1_fixed_points =
{
    5u, 384u, 77u, 39u,
    (NX_CRYPTO_EC_POINT *)secp384r1_fixed_points_array,
    (NX_CRYPTO_EC_POINT *)secp384r1_fixed_points_2e_array
};

