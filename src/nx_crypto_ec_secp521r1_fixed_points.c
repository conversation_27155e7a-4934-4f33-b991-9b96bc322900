/***************************************************************************
 * Copyright (c) 2024 Microsoft Corporation 
 * Copyright (c) 2025-present Eclipse ThreadX Contributors
 * 
 * This program and the accompanying materials are made available under the
 * terms of the MIT License which is available at
 * https://opensource.org/licenses/MIT.
 * 
 * SPDX-License-Identifier: MIT
 **************************************************************************/


/**************************************************************************/
/**************************************************************************/
/**                                                                       */
/** NetX Crypto Component                                                 */
/**                                                                       */
/**   Elliptical Curve Cryptography                                       */
/**                                                                       */
/**************************************************************************/
/**************************************************************************/

#include "nx_crypto_ec.h"
static NX_CRYPTO_CONST HN_UBASE           secp521r1_fixed_points_data[][68 >> HN_SIZE_SHIFT] =
{

    /* 2G.x */
    {
        HN_ULONG_TO_UBASE(0x876B33AC), HN_ULONG_TO_UBASE(0x45D90CF8),
        HN_ULONG_TO_UBASE(0x3ED58F0B), HN_ULONG_TO_UBASE(0xE53E1A99),
        HN_ULONG_TO_UBASE(0x49D916B3), HN_ULONG_TO_UBASE(0xD5D181F5),
        HN_ULONG_TO_UBASE(0x2EC09BE2), HN_ULONG_TO_UBASE(0x1B1EF040),
        HN_ULONG_TO_UBASE(0xB2113B57), HN_ULONG_TO_UBASE(0xE5787176),
        HN_ULONG_TO_UBASE(0x8073044E), HN_ULONG_TO_UBASE(0x11D02E70),
        HN_ULONG_TO_UBASE(0xA2AE38DD), HN_ULONG_TO_UBASE(0x9D1C19E7),
        HN_ULONG_TO_UBASE(0x3634F6FA), HN_ULONG_TO_UBASE(0x2662D494),
        HN_ULONG_TO_UBASE(0x00000130)
    },

    /* 2G.y */
    {
        HN_ULONG_TO_UBASE(0x6BD6208E), HN_ULONG_TO_UBASE(0x454C8A73),
        HN_ULONG_TO_UBASE(0xAE37911A), HN_ULONG_TO_UBASE(0xD2447CEF),
        HN_ULONG_TO_UBASE(0x69B8C5AE), HN_ULONG_TO_UBASE(0x56AC592E),
        HN_ULONG_TO_UBASE(0x9B615BFC), HN_ULONG_TO_UBASE(0xE7F56483),
        HN_ULONG_TO_UBASE(0xBBE7FE62), HN_ULONG_TO_UBASE(0xFAC066E1),
        HN_ULONG_TO_UBASE(0xB7777B32), HN_ULONG_TO_UBASE(0xB2AC3EE1),
        HN_ULONG_TO_UBASE(0x15114ADA), HN_ULONG_TO_UBASE(0x55F86533),
        HN_ULONG_TO_UBASE(0x46497CC9), HN_ULONG_TO_UBASE(0x15CBA83E),
        HN_ULONG_TO_UBASE(0x000001EF)
    },

    /* 3G.x */
    {
        HN_ULONG_TO_UBASE(0xE371375D), HN_ULONG_TO_UBASE(0x863B3ADB),
        HN_ULONG_TO_UBASE(0x89CF187B), HN_ULONG_TO_UBASE(0xF2DDD842),
        HN_ULONG_TO_UBASE(0x6AB24B10), HN_ULONG_TO_UBASE(0x80CF70E7),
        HN_ULONG_TO_UBASE(0x57D81A93), HN_ULONG_TO_UBASE(0x78063AE6),
        HN_ULONG_TO_UBASE(0xC87491C5), HN_ULONG_TO_UBASE(0x88AEB0B1),
        HN_ULONG_TO_UBASE(0x2E17FBF9), HN_ULONG_TO_UBASE(0x5EBE733A),
        HN_ULONG_TO_UBASE(0x52AD31BA), HN_ULONG_TO_UBASE(0xC205CBCD),
        HN_ULONG_TO_UBASE(0xED66A7F8), HN_ULONG_TO_UBASE(0xA13230F9),
        HN_ULONG_TO_UBASE(0x00000117)
    },

    /* 3G.y */
    {
        HN_ULONG_TO_UBASE(0x03A6EC5D), HN_ULONG_TO_UBASE(0x224D9A3B),
        HN_ULONG_TO_UBASE(0xBE627BE3), HN_ULONG_TO_UBASE(0xE1EFCA41),
        HN_ULONG_TO_UBASE(0x22ABCA89), HN_ULONG_TO_UBASE(0x34DF229C),
        HN_ULONG_TO_UBASE(0x8153B848), HN_ULONG_TO_UBASE(0x11C526A0),
        HN_ULONG_TO_UBASE(0xCBB59E96), HN_ULONG_TO_UBASE(0x26879BD9),
        HN_ULONG_TO_UBASE(0x8D1298ED), HN_ULONG_TO_UBASE(0x77ECCC36),
        HN_ULONG_TO_UBASE(0xD6560F21), HN_ULONG_TO_UBASE(0xAF68677B),
        HN_ULONG_TO_UBASE(0x242B365B), HN_ULONG_TO_UBASE(0xE5DDC615),
        HN_ULONG_TO_UBASE(0x0000017F)
    },

    /* 4G.x */
    {
        HN_ULONG_TO_UBASE(0x155C337B), HN_ULONG_TO_UBASE(0x7EAEFE12),
        HN_ULONG_TO_UBASE(0xC7186596), HN_ULONG_TO_UBASE(0x357F27CE),
        HN_ULONG_TO_UBASE(0x654DBBEB), HN_ULONG_TO_UBASE(0x90C26C69),
        HN_ULONG_TO_UBASE(0xA1B63A6A), HN_ULONG_TO_UBASE(0x6BCCA278),
        HN_ULONG_TO_UBASE(0x8EDD9123), HN_ULONG_TO_UBASE(0x776B7A92),
        HN_ULONG_TO_UBASE(0x6C6DA5E9), HN_ULONG_TO_UBASE(0xB2BEF507),
        HN_ULONG_TO_UBASE(0x0E3C747B), HN_ULONG_TO_UBASE(0x805ED3AA),
        HN_ULONG_TO_UBASE(0x659DF2EF), HN_ULONG_TO_UBASE(0x298EF458),
        HN_ULONG_TO_UBASE(0x00000098)
    },

    /* 4G.y */
    {
        HN_ULONG_TO_UBASE(0x35A79F8D), HN_ULONG_TO_UBASE(0xE3DD8939),
        HN_ULONG_TO_UBASE(0xA1972C6B), HN_ULONG_TO_UBASE(0x672B109C),
        HN_ULONG_TO_UBASE(0x07953A4D), HN_ULONG_TO_UBASE(0xAD41CBAE),
        HN_ULONG_TO_UBASE(0xD206DE77), HN_ULONG_TO_UBASE(0xDE07527F),
        HN_ULONG_TO_UBASE(0xB61D9811), HN_ULONG_TO_UBASE(0x1F55A4C0),
        HN_ULONG_TO_UBASE(0x7A75360E), HN_ULONG_TO_UBASE(0x929D56D5),
        HN_ULONG_TO_UBASE(0x5032EFE6), HN_ULONG_TO_UBASE(0x9F2BECFF),
        HN_ULONG_TO_UBASE(0x60F0622C), HN_ULONG_TO_UBASE(0x0DE654C7),
        HN_ULONG_TO_UBASE(0x00000018)
    },

    /* 5G.x */
    {
        HN_ULONG_TO_UBASE(0x8445216B), HN_ULONG_TO_UBASE(0xD9E465AF),
        HN_ULONG_TO_UBASE(0xC57AE51B), HN_ULONG_TO_UBASE(0x7D12C12B),
        HN_ULONG_TO_UBASE(0xAB01F4D2), HN_ULONG_TO_UBASE(0xAF65FD9B),
        HN_ULONG_TO_UBASE(0xB3C1D722), HN_ULONG_TO_UBASE(0xF2FD8A84),
        HN_ULONG_TO_UBASE(0xF5123832), HN_ULONG_TO_UBASE(0x8870DC27),
        HN_ULONG_TO_UBASE(0x8C85325E), HN_ULONG_TO_UBASE(0xE8B51D16),
        HN_ULONG_TO_UBASE(0x92E31759), HN_ULONG_TO_UBASE(0x1A03D5DF),
        HN_ULONG_TO_UBASE(0xEFDB49C3), HN_ULONG_TO_UBASE(0x7B5E6C46),
        HN_ULONG_TO_UBASE(0x0000005E)
    },

    /* 5G.y */
    {
        HN_ULONG_TO_UBASE(0x137A651A), HN_ULONG_TO_UBASE(0xB427174D),
        HN_ULONG_TO_UBASE(0x256229CB), HN_ULONG_TO_UBASE(0xF9E661D4),
        HN_ULONG_TO_UBASE(0xED3AEB3F), HN_ULONG_TO_UBASE(0xBBD04D0F),
        HN_ULONG_TO_UBASE(0x5BB3FAE1), HN_ULONG_TO_UBASE(0xC31EEF45),
        HN_ULONG_TO_UBASE(0x07A35089), HN_ULONG_TO_UBASE(0x52DF99E4),
        HN_ULONG_TO_UBASE(0xED429E1A), HN_ULONG_TO_UBASE(0x6514B9BD),
        HN_ULONG_TO_UBASE(0x67ECBD97), HN_ULONG_TO_UBASE(0x757977BF),
        HN_ULONG_TO_UBASE(0xB2725C6D), HN_ULONG_TO_UBASE(0x70A2B55F),
        HN_ULONG_TO_UBASE(0x000000A7)
    },

    /* 6G.x */
    {
        HN_ULONG_TO_UBASE(0x8913355A), HN_ULONG_TO_UBASE(0xDD2CD8AE),
        HN_ULONG_TO_UBASE(0xBA2F3522), HN_ULONG_TO_UBASE(0x02114BFC),
        HN_ULONG_TO_UBASE(0x22446ABF), HN_ULONG_TO_UBASE(0xE62184F5),
        HN_ULONG_TO_UBASE(0x68E6D0C5), HN_ULONG_TO_UBASE(0xE8D75651),
        HN_ULONG_TO_UBASE(0x19AD9EBC), HN_ULONG_TO_UBASE(0x580F6C03),
        HN_ULONG_TO_UBASE(0xC2953557), HN_ULONG_TO_UBASE(0x8B9D5D55),
        HN_ULONG_TO_UBASE(0x618534DF), HN_ULONG_TO_UBASE(0x234640A5),
        HN_ULONG_TO_UBASE(0xFED9E6B6), HN_ULONG_TO_UBASE(0xF452A772),
        HN_ULONG_TO_UBASE(0x000001B7)
    },

    /* 6G.y */
    {
        HN_ULONG_TO_UBASE(0x16211BB5), HN_ULONG_TO_UBASE(0x0952E477),
        HN_ULONG_TO_UBASE(0x5CEDF594), HN_ULONG_TO_UBASE(0xD3D874AE),
        HN_ULONG_TO_UBASE(0x85328765), HN_ULONG_TO_UBASE(0x2C21A7BD),
        HN_ULONG_TO_UBASE(0xEC333D98), HN_ULONG_TO_UBASE(0x44B3033C),
        HN_ULONG_TO_UBASE(0x3C587CD5), HN_ULONG_TO_UBASE(0x0474258D),
        HN_ULONG_TO_UBASE(0x4962E19E), HN_ULONG_TO_UBASE(0x527C162F),
        HN_ULONG_TO_UBASE(0x5B356DD3), HN_ULONG_TO_UBASE(0x0DBF0F7A),
        HN_ULONG_TO_UBASE(0x95BBFC9B), HN_ULONG_TO_UBASE(0x8D683B71),
        HN_ULONG_TO_UBASE(0x000001BC)
    },

    /* 7G.x */
    {
        HN_ULONG_TO_UBASE(0x94BB174C), HN_ULONG_TO_UBASE(0xFF99F7CD),
        HN_ULONG_TO_UBASE(0x96566E5E), HN_ULONG_TO_UBASE(0x905B42BD),
        HN_ULONG_TO_UBASE(0xEBAE3BAA), HN_ULONG_TO_UBASE(0xDA1ABF35),
        HN_ULONG_TO_UBASE(0x7644CE21), HN_ULONG_TO_UBASE(0x15DDF71B),
        HN_ULONG_TO_UBASE(0x50E83A0C), HN_ULONG_TO_UBASE(0x2920C1C0),
        HN_ULONG_TO_UBASE(0x0F7B1973), HN_ULONG_TO_UBASE(0x237776ED),
        HN_ULONG_TO_UBASE(0x57ADF3A6), HN_ULONG_TO_UBASE(0x7E393449),
        HN_ULONG_TO_UBASE(0x9160EAD0), HN_ULONG_TO_UBASE(0xD9E08CF9),
        HN_ULONG_TO_UBASE(0x00000100)
    },

    /* 7G.y */
    {
        HN_ULONG_TO_UBASE(0x675707DA), HN_ULONG_TO_UBASE(0x54F0135E),
        HN_ULONG_TO_UBASE(0x2E5596B0), HN_ULONG_TO_UBASE(0x5976788E),
        HN_ULONG_TO_UBASE(0xF3AC4001), HN_ULONG_TO_UBASE(0x73DD4B88),
        HN_ULONG_TO_UBASE(0xFBACBFE7), HN_ULONG_TO_UBASE(0x7F5453E3),
        HN_ULONG_TO_UBASE(0x426B2080), HN_ULONG_TO_UBASE(0x551DC249),
        HN_ULONG_TO_UBASE(0xE33F8F92), HN_ULONG_TO_UBASE(0x528FF571),
        HN_ULONG_TO_UBASE(0x95834F88), HN_ULONG_TO_UBASE(0xD78D1E40),
        HN_ULONG_TO_UBASE(0x7D07A77C), HN_ULONG_TO_UBASE(0x20405C91),
        HN_ULONG_TO_UBASE(0x00000114)
    },

    /* 8G.x */
    {
        HN_ULONG_TO_UBASE(0xEC3D1383), HN_ULONG_TO_UBASE(0xD1CE126E),
        HN_ULONG_TO_UBASE(0x4805B18E), HN_ULONG_TO_UBASE(0x0C7F980A),
        HN_ULONG_TO_UBASE(0xFC1B1F4E), HN_ULONG_TO_UBASE(0x65945086),
        HN_ULONG_TO_UBASE(0x092E0CA0), HN_ULONG_TO_UBASE(0xAC1703AE),
        HN_ULONG_TO_UBASE(0x8B5EE5C0), HN_ULONG_TO_UBASE(0x834C77F9),
        HN_ULONG_TO_UBASE(0x6D19FBB5), HN_ULONG_TO_UBASE(0x3E722F57),
        HN_ULONG_TO_UBASE(0xF6770BD1), HN_ULONG_TO_UBASE(0xAE8A944A),
        HN_ULONG_TO_UBASE(0x2A7C7101), HN_ULONG_TO_UBASE(0xE1D11050),
        HN_ULONG_TO_UBASE(0x000000D7)
    },

    /* 8G.y */
    {
        HN_ULONG_TO_UBASE(0xCA303000), HN_ULONG_TO_UBASE(0x62029FD2),
        HN_ULONG_TO_UBASE(0xACB52ECD), HN_ULONG_TO_UBASE(0x366C72C9),
        HN_ULONG_TO_UBASE(0xCC8DD8A2), HN_ULONG_TO_UBASE(0xCF89C1AA),
        HN_ULONG_TO_UBASE(0x5D1B984D), HN_ULONG_TO_UBASE(0x95C235BF),
        HN_ULONG_TO_UBASE(0xD1A80D52), HN_ULONG_TO_UBASE(0x3434D10D),
        HN_ULONG_TO_UBASE(0x77E95ADD), HN_ULONG_TO_UBASE(0x094D0A8F),
        HN_ULONG_TO_UBASE(0xD1203660), HN_ULONG_TO_UBASE(0x03890027),
        HN_ULONG_TO_UBASE(0x29791AB3), HN_ULONG_TO_UBASE(0x32FAF273),
        HN_ULONG_TO_UBASE(0x00000075)
    },

    /* 9G.x */
    {
        HN_ULONG_TO_UBASE(0x075A4208), HN_ULONG_TO_UBASE(0x9281B5C1),
        HN_ULONG_TO_UBASE(0x471C7FCC), HN_ULONG_TO_UBASE(0xAFC5E0E5),
        HN_ULONG_TO_UBASE(0xC8704F00), HN_ULONG_TO_UBASE(0x9A6E82F3),
        HN_ULONG_TO_UBASE(0x62960946), HN_ULONG_TO_UBASE(0x4320C31A),
        HN_ULONG_TO_UBASE(0x513C571F), HN_ULONG_TO_UBASE(0xE673B4BD),
        HN_ULONG_TO_UBASE(0x3DC4D8FB), HN_ULONG_TO_UBASE(0x9B68532A),
        HN_ULONG_TO_UBASE(0x2C95DBE9), HN_ULONG_TO_UBASE(0xA8CBE344),
        HN_ULONG_TO_UBASE(0x229C47B7), HN_ULONG_TO_UBASE(0x0F6AC257),
        HN_ULONG_TO_UBASE(0x0000014A)
    },

    /* 9G.y */
    {
        HN_ULONG_TO_UBASE(0xC1411542), HN_ULONG_TO_UBASE(0x1B4C092D),
        HN_ULONG_TO_UBASE(0x7747BEAF), HN_ULONG_TO_UBASE(0xC4BB45D6),
        HN_ULONG_TO_UBASE(0x8D55735E), HN_ULONG_TO_UBASE(0x685C4BE4),
        HN_ULONG_TO_UBASE(0x6383FD44), HN_ULONG_TO_UBASE(0x9E72FCC8),
        HN_ULONG_TO_UBASE(0x203FF740), HN_ULONG_TO_UBASE(0x615BDC52),
        HN_ULONG_TO_UBASE(0x5B1A64AD), HN_ULONG_TO_UBASE(0xCC19DAFB),
        HN_ULONG_TO_UBASE(0x2B782E96), HN_ULONG_TO_UBASE(0x28EF0D34),
        HN_ULONG_TO_UBASE(0x05E3AE87), HN_ULONG_TO_UBASE(0x21D84532),
        HN_ULONG_TO_UBASE(0x00000052)
    },

    /* 10G.x */
    {
        HN_ULONG_TO_UBASE(0x72656971), HN_ULONG_TO_UBASE(0x6A884570),
        HN_ULONG_TO_UBASE(0xA7F20EFE), HN_ULONG_TO_UBASE(0xDC4FCB92),
        HN_ULONG_TO_UBASE(0xDC31417C), HN_ULONG_TO_UBASE(0x759DB4FF),
        HN_ULONG_TO_UBASE(0xF95DC14F), HN_ULONG_TO_UBASE(0xF1BF0345),
        HN_ULONG_TO_UBASE(0x0FD656F0), HN_ULONG_TO_UBASE(0x0686BD5B),
        HN_ULONG_TO_UBASE(0x6F4440F7), HN_ULONG_TO_UBASE(0x9C5EA036),
        HN_ULONG_TO_UBASE(0x3887F6E1), HN_ULONG_TO_UBASE(0x207619CE),
        HN_ULONG_TO_UBASE(0xA211DE2D), HN_ULONG_TO_UBASE(0xA9EF8D60),
        HN_ULONG_TO_UBASE(0x0000007D)
    },

    /* 10G.y */
    {
        HN_ULONG_TO_UBASE(0x8E5A6C11), HN_ULONG_TO_UBASE(0xD0BCE7F9),
        HN_ULONG_TO_UBASE(0xEDC82B6C), HN_ULONG_TO_UBASE(0xAB338406),
        HN_ULONG_TO_UBASE(0x7CB589B2), HN_ULONG_TO_UBASE(0x36726A50),
        HN_ULONG_TO_UBASE(0x02CC7EB7), HN_ULONG_TO_UBASE(0x887B6CFC),
        HN_ULONG_TO_UBASE(0x28ACE2AB), HN_ULONG_TO_UBASE(0x651F7903),
        HN_ULONG_TO_UBASE(0x06BA6057), HN_ULONG_TO_UBASE(0x8E7F8436),
        HN_ULONG_TO_UBASE(0xBFF72AFE), HN_ULONG_TO_UBASE(0xD103B1B2),
        HN_ULONG_TO_UBASE(0x46959145), HN_ULONG_TO_UBASE(0x942CF0C6),
        HN_ULONG_TO_UBASE(0x00000033)
    },

    /* 11G.x */
    {
        HN_ULONG_TO_UBASE(0x5805D03E), HN_ULONG_TO_UBASE(0x204B0833),
        HN_ULONG_TO_UBASE(0x5A1AB8CF), HN_ULONG_TO_UBASE(0x3C03AE7A),
        HN_ULONG_TO_UBASE(0xEAD6F888), HN_ULONG_TO_UBASE(0x3376E27B),
        HN_ULONG_TO_UBASE(0x94A53E60), HN_ULONG_TO_UBASE(0xB0E6713A),
        HN_ULONG_TO_UBASE(0x3F0B8EE9), HN_ULONG_TO_UBASE(0x790F9E81),
        HN_ULONG_TO_UBASE(0x4B4896E3), HN_ULONG_TO_UBASE(0x21B2AC27),
        HN_ULONG_TO_UBASE(0xE7FAFECC), HN_ULONG_TO_UBASE(0xCE7C291B),
        HN_ULONG_TO_UBASE(0x1C1049CA), HN_ULONG_TO_UBASE(0x10FE14A2),
        HN_ULONG_TO_UBASE(0x0000004D)
    },

    /* 11G.y */
    {
        HN_ULONG_TO_UBASE(0xD2130D68), HN_ULONG_TO_UBASE(0x95BB30A7),
        HN_ULONG_TO_UBASE(0x4C891468), HN_ULONG_TO_UBASE(0xC2A8F2A5),
        HN_ULONG_TO_UBASE(0xB1608A4A), HN_ULONG_TO_UBASE(0x5236AE15),
        HN_ULONG_TO_UBASE(0xC50F2485), HN_ULONG_TO_UBASE(0xBD832829),
        HN_ULONG_TO_UBASE(0x435254ED), HN_ULONG_TO_UBASE(0xC87AF748),
        HN_ULONG_TO_UBASE(0x3A80DC29), HN_ULONG_TO_UBASE(0xC76D7DF6),
        HN_ULONG_TO_UBASE(0x5D881936), HN_ULONG_TO_UBASE(0x7CE57B49),
        HN_ULONG_TO_UBASE(0xC0AEAA0D), HN_ULONG_TO_UBASE(0x8E95CB82),
        HN_ULONG_TO_UBASE(0x0000017F)
    },

    /* 12G.x */
    {
        HN_ULONG_TO_UBASE(0x829A40A3), HN_ULONG_TO_UBASE(0x21A193C7),
        HN_ULONG_TO_UBASE(0x05F32F41), HN_ULONG_TO_UBASE(0x87B759A9),
        HN_ULONG_TO_UBASE(0x6E143537), HN_ULONG_TO_UBASE(0xBA154763),
        HN_ULONG_TO_UBASE(0x6B5A6433), HN_ULONG_TO_UBASE(0xFB951864),
        HN_ULONG_TO_UBASE(0xD8E9BE43), HN_ULONG_TO_UBASE(0x81C1DADF),
        HN_ULONG_TO_UBASE(0xB5173D47), HN_ULONG_TO_UBASE(0x3145A42C),
        HN_ULONG_TO_UBASE(0xE5C9B799), HN_ULONG_TO_UBASE(0x22D5EF4B),
        HN_ULONG_TO_UBASE(0xFFE36EE4), HN_ULONG_TO_UBASE(0x0BDE5942),
        HN_ULONG_TO_UBASE(0x0000010A)
    },

    /* 12G.y */
    {
        HN_ULONG_TO_UBASE(0xF4BC58E5), HN_ULONG_TO_UBASE(0xAFC83BA5),
        HN_ULONG_TO_UBASE(0xD58698E3), HN_ULONG_TO_UBASE(0xB418E478),
        HN_ULONG_TO_UBASE(0x1726D889), HN_ULONG_TO_UBASE(0x1F60371A),
        HN_ULONG_TO_UBASE(0x2F0A291A), HN_ULONG_TO_UBASE(0xA5A58B57),
        HN_ULONG_TO_UBASE(0xFD356626), HN_ULONG_TO_UBASE(0x0E44DA0A),
        HN_ULONG_TO_UBASE(0x2D89342F), HN_ULONG_TO_UBASE(0xA3E9BE34),
        HN_ULONG_TO_UBASE(0xC3C1B4A2), HN_ULONG_TO_UBASE(0x9195921B),
        HN_ULONG_TO_UBASE(0x25E4191E), HN_ULONG_TO_UBASE(0xBCB21228),
        HN_ULONG_TO_UBASE(0x00000135)
    },

    /* 13G.x */
    {
        HN_ULONG_TO_UBASE(0x0EBA209A), HN_ULONG_TO_UBASE(0xA1B5F88D),
        HN_ULONG_TO_UBASE(0x3C5E2880), HN_ULONG_TO_UBASE(0x04B54668),
        HN_ULONG_TO_UBASE(0xEFBB25F1), HN_ULONG_TO_UBASE(0xE93591A0),
        HN_ULONG_TO_UBASE(0xE9729982), HN_ULONG_TO_UBASE(0x1678D5F7),
        HN_ULONG_TO_UBASE(0x7D430831), HN_ULONG_TO_UBASE(0x6EB992DA),
        HN_ULONG_TO_UBASE(0x0B7C198F), HN_ULONG_TO_UBASE(0x1A4A91F7),
        HN_ULONG_TO_UBASE(0x6ED1FF3E), HN_ULONG_TO_UBASE(0x3A679847),
        HN_ULONG_TO_UBASE(0x5465E131), HN_ULONG_TO_UBASE(0x18A5E132),
        HN_ULONG_TO_UBASE(0x00000042)
    },

    /* 13G.y */
    {
        HN_ULONG_TO_UBASE(0x59486FC8), HN_ULONG_TO_UBASE(0xAF8471EB),
        HN_ULONG_TO_UBASE(0x9B6AB9E2), HN_ULONG_TO_UBASE(0x397D8CAB),
        HN_ULONG_TO_UBASE(0x10C0F9EA), HN_ULONG_TO_UBASE(0x942279C2),
        HN_ULONG_TO_UBASE(0xDDDF11B9), HN_ULONG_TO_UBASE(0xB3186547),
        HN_ULONG_TO_UBASE(0x7E0E49B0), HN_ULONG_TO_UBASE(0x2910861A),
        HN_ULONG_TO_UBASE(0x4C374108), HN_ULONG_TO_UBASE(0x0AC066AB),
        HN_ULONG_TO_UBASE(0x9C34F007), HN_ULONG_TO_UBASE(0x67C76F4C),
        HN_ULONG_TO_UBASE(0x6A9E031F), HN_ULONG_TO_UBASE(0x3635EDBE),
        HN_ULONG_TO_UBASE(0x000000F7)
    },

    /* 14G.x */
    {
        HN_ULONG_TO_UBASE(0x4CCFA596), HN_ULONG_TO_UBASE(0x2E91929F),
        HN_ULONG_TO_UBASE(0x844098CF), HN_ULONG_TO_UBASE(0x3B168733),
        HN_ULONG_TO_UBASE(0x616A36DF), HN_ULONG_TO_UBASE(0x0FEA437E),
        HN_ULONG_TO_UBASE(0xE0DC39AE), HN_ULONG_TO_UBASE(0xBEA5755D),
        HN_ULONG_TO_UBASE(0xCA20C73A), HN_ULONG_TO_UBASE(0x721050E6),
        HN_ULONG_TO_UBASE(0xA6534DE2), HN_ULONG_TO_UBASE(0x5D86BB64),
        HN_ULONG_TO_UBASE(0xAF758AEF), HN_ULONG_TO_UBASE(0x0C65FF4F),
        HN_ULONG_TO_UBASE(0x33832CCA), HN_ULONG_TO_UBASE(0x38D7BD4D),
        HN_ULONG_TO_UBASE(0x00000127)
    },

    /* 14G.y */
    {
        HN_ULONG_TO_UBASE(0xF7B22FC2), HN_ULONG_TO_UBASE(0xAABE7E25),
        HN_ULONG_TO_UBASE(0x138537BE), HN_ULONG_TO_UBASE(0x44EC7B6E),
        HN_ULONG_TO_UBASE(0x5AD7C324), HN_ULONG_TO_UBASE(0x33CD05C9),
        HN_ULONG_TO_UBASE(0xC1602459), HN_ULONG_TO_UBASE(0x28A6115C),
        HN_ULONG_TO_UBASE(0xDF229461), HN_ULONG_TO_UBASE(0x05C1AA34),
        HN_ULONG_TO_UBASE(0xBDB1D24D), HN_ULONG_TO_UBASE(0x39FC35D8),
        HN_ULONG_TO_UBASE(0x4B5F6223), HN_ULONG_TO_UBASE(0x536AA0D5),
        HN_ULONG_TO_UBASE(0x703BD0F3), HN_ULONG_TO_UBASE(0x1D5287E3),
        HN_ULONG_TO_UBASE(0x00000186)
    },

    /* 15G.x */
    {
        HN_ULONG_TO_UBASE(0x5E6A7807), HN_ULONG_TO_UBASE(0x9F9A072D),
        HN_ULONG_TO_UBASE(0x39EEB105), HN_ULONG_TO_UBASE(0xF9A38A83),
        HN_ULONG_TO_UBASE(0x17DDB1B4), HN_ULONG_TO_UBASE(0x1503495F),
        HN_ULONG_TO_UBASE(0x732310B4), HN_ULONG_TO_UBASE(0xDA780A6C),
        HN_ULONG_TO_UBASE(0x403A5D57), HN_ULONG_TO_UBASE(0x7B287813),
        HN_ULONG_TO_UBASE(0xE7C481E7), HN_ULONG_TO_UBASE(0x6BB08815),
        HN_ULONG_TO_UBASE(0x574C23E2), HN_ULONG_TO_UBASE(0x1198C8A4),
        HN_ULONG_TO_UBASE(0x673DFC44), HN_ULONG_TO_UBASE(0xA8DA92A4),
        HN_ULONG_TO_UBASE(0x0000002A)
    },

    /* 15G.y */
    {
        HN_ULONG_TO_UBASE(0xBFB7CE31), HN_ULONG_TO_UBASE(0x92DF33AE),
        HN_ULONG_TO_UBASE(0x4A686F13), HN_ULONG_TO_UBASE(0x1648E528),
        HN_ULONG_TO_UBASE(0xE45BA7F2), HN_ULONG_TO_UBASE(0x4429B3AF),
        HN_ULONG_TO_UBASE(0x397C1D83), HN_ULONG_TO_UBASE(0x216C5137),
        HN_ULONG_TO_UBASE(0xC8C8EE26), HN_ULONG_TO_UBASE(0xCAC9D3C3),
        HN_ULONG_TO_UBASE(0x40C73424), HN_ULONG_TO_UBASE(0x0711605E),
        HN_ULONG_TO_UBASE(0x219C8C3F), HN_ULONG_TO_UBASE(0x008B93A5),
        HN_ULONG_TO_UBASE(0xC6F10BB3), HN_ULONG_TO_UBASE(0xAB1EE7B8),
        HN_ULONG_TO_UBASE(0x000000E0)
    },

    /* 16G.x */
    {
        HN_ULONG_TO_UBASE(0x10A8C4FB), HN_ULONG_TO_UBASE(0x73A6BA38),
        HN_ULONG_TO_UBASE(0xECC93E5D), HN_ULONG_TO_UBASE(0x5153D959),
        HN_ULONG_TO_UBASE(0xB59E9871), HN_ULONG_TO_UBASE(0x7CA58012),
        HN_ULONG_TO_UBASE(0xAFD442F1), HN_ULONG_TO_UBASE(0xEDC0DBEF),
        HN_ULONG_TO_UBASE(0xB9CF7691), HN_ULONG_TO_UBASE(0xB9050A22),
        HN_ULONG_TO_UBASE(0x464D017D), HN_ULONG_TO_UBASE(0x3D1E96FE),
        HN_ULONG_TO_UBASE(0x82074DCA), HN_ULONG_TO_UBASE(0x541781A4),
        HN_ULONG_TO_UBASE(0x8B355413), HN_ULONG_TO_UBASE(0xEDCE0DB3),
        HN_ULONG_TO_UBASE(0x0000006B)
    },

    /* 16G.y */
    {
        HN_ULONG_TO_UBASE(0xAE2B39C2), HN_ULONG_TO_UBASE(0x1A13E3EE),
        HN_ULONG_TO_UBASE(0x3C218179), HN_ULONG_TO_UBASE(0xC431081D),
        HN_ULONG_TO_UBASE(0xAE68B7C6), HN_ULONG_TO_UBASE(0x5CBC14C1),
        HN_ULONG_TO_UBASE(0x9005A304), HN_ULONG_TO_UBASE(0xCF2559BB),
        HN_ULONG_TO_UBASE(0x2EC7AED5), HN_ULONG_TO_UBASE(0x14D7C1E9),
        HN_ULONG_TO_UBASE(0x1E2E2F0D), HN_ULONG_TO_UBASE(0x5C379BFE),
        HN_ULONG_TO_UBASE(0x886F0CF9), HN_ULONG_TO_UBASE(0xFC33E4D2),
        HN_ULONG_TO_UBASE(0xAC4E1D17), HN_ULONG_TO_UBASE(0x2F14E7D0),
        HN_ULONG_TO_UBASE(0x00000071)
    },

    /* 17G.x */
    {
        HN_ULONG_TO_UBASE(0xEDDE488A), HN_ULONG_TO_UBASE(0x2F1F1497),
        HN_ULONG_TO_UBASE(0x31EE698E), HN_ULONG_TO_UBASE(0x3D0364B2),
        HN_ULONG_TO_UBASE(0xA47E048E), HN_ULONG_TO_UBASE(0x88A32C39),
        HN_ULONG_TO_UBASE(0x86DA37C4), HN_ULONG_TO_UBASE(0x80ABD8DE),
        HN_ULONG_TO_UBASE(0x07895C9C), HN_ULONG_TO_UBASE(0x6608EED9),
        HN_ULONG_TO_UBASE(0xD18A7081), HN_ULONG_TO_UBASE(0xCCA6B9E6),
        HN_ULONG_TO_UBASE(0x0CA87303), HN_ULONG_TO_UBASE(0x44F63AA9),
        HN_ULONG_TO_UBASE(0x094F9789), HN_ULONG_TO_UBASE(0x84281EED),
        HN_ULONG_TO_UBASE(0x0000017F)
    },

    /* 17G.y */
    {
        HN_ULONG_TO_UBASE(0x6AA5F7A3), HN_ULONG_TO_UBASE(0x5703727D),
        HN_ULONG_TO_UBASE(0x09DA94A2), HN_ULONG_TO_UBASE(0xD9C33512),
        HN_ULONG_TO_UBASE(0xADDCCFD6), HN_ULONG_TO_UBASE(0x80572F9E),
        HN_ULONG_TO_UBASE(0x45FEBCC1), HN_ULONG_TO_UBASE(0xF95BF8B0),
        HN_ULONG_TO_UBASE(0x30A48DAC), HN_ULONG_TO_UBASE(0x4BCD4B12),
        HN_ULONG_TO_UBASE(0xF00F8619), HN_ULONG_TO_UBASE(0x32A16A21),
        HN_ULONG_TO_UBASE(0x1BCAD341), HN_ULONG_TO_UBASE(0x612D82B9),
        HN_ULONG_TO_UBASE(0xE2BABC4A), HN_ULONG_TO_UBASE(0xF42138DA),
        HN_ULONG_TO_UBASE(0x0000014B)
    },

    /* 18G.x */
    {
        HN_ULONG_TO_UBASE(0x1510E086), HN_ULONG_TO_UBASE(0x7E9364EA),
        HN_ULONG_TO_UBASE(0x31D0F679), HN_ULONG_TO_UBASE(0x524B63B1),
        HN_ULONG_TO_UBASE(0x9AEAE146), HN_ULONG_TO_UBASE(0xF8F3CC52),
        HN_ULONG_TO_UBASE(0x36F90818), HN_ULONG_TO_UBASE(0x8C05C88D),
        HN_ULONG_TO_UBASE(0x80D7DAB5), HN_ULONG_TO_UBASE(0x16ED75E5),
        HN_ULONG_TO_UBASE(0x54000C49), HN_ULONG_TO_UBASE(0x8F872700),
        HN_ULONG_TO_UBASE(0x843C5F7A), HN_ULONG_TO_UBASE(0x9740A2A1),
        HN_ULONG_TO_UBASE(0x6E680411), HN_ULONG_TO_UBASE(0x8DC46AC5),
        HN_ULONG_TO_UBASE(0x000000D8)
    },

    /* 18G.y */
    {
        HN_ULONG_TO_UBASE(0xBAC1FCBC), HN_ULONG_TO_UBASE(0x9492A1D6),
        HN_ULONG_TO_UBASE(0x8A9CA207), HN_ULONG_TO_UBASE(0xCD7CD811),
        HN_ULONG_TO_UBASE(0xCDED0753), HN_ULONG_TO_UBASE(0xD61403A1),
        HN_ULONG_TO_UBASE(0x6702B3FA), HN_ULONG_TO_UBASE(0x2D232E49),
        HN_ULONG_TO_UBASE(0x83E33229), HN_ULONG_TO_UBASE(0x5BD01FC9),
        HN_ULONG_TO_UBASE(0x4DBE98F9), HN_ULONG_TO_UBASE(0x157C427C),
        HN_ULONG_TO_UBASE(0x1C5B4229), HN_ULONG_TO_UBASE(0xD2594A9F),
        HN_ULONG_TO_UBASE(0xE6318047), HN_ULONG_TO_UBASE(0xE0207B85),
        HN_ULONG_TO_UBASE(0x000001F5)
    },

    /* 19G.x */
    {
        HN_ULONG_TO_UBASE(0x71AAEF75), HN_ULONG_TO_UBASE(0x6BF98381),
        HN_ULONG_TO_UBASE(0xC14B076A), HN_ULONG_TO_UBASE(0x479CC2A8),
        HN_ULONG_TO_UBASE(0xCC783DC5), HN_ULONG_TO_UBASE(0xC0466EC4),
        HN_ULONG_TO_UBASE(0x682E48F9), HN_ULONG_TO_UBASE(0x7F120DA2),
        HN_ULONG_TO_UBASE(0x4A4ED12F), HN_ULONG_TO_UBASE(0xE02258AC),
        HN_ULONG_TO_UBASE(0x83DA05ED), HN_ULONG_TO_UBASE(0x8D8AB9B9),
        HN_ULONG_TO_UBASE(0x394701B0), HN_ULONG_TO_UBASE(0xB1B1EC54),
        HN_ULONG_TO_UBASE(0x82D2C76E), HN_ULONG_TO_UBASE(0x8FC99926),
        HN_ULONG_TO_UBASE(0x00000079)
    },

    /* 19G.y */
    {
        HN_ULONG_TO_UBASE(0x96C5465A), HN_ULONG_TO_UBASE(0x0A611BF4),
        HN_ULONG_TO_UBASE(0x74F20F8F), HN_ULONG_TO_UBASE(0xD0A6B210),
        HN_ULONG_TO_UBASE(0xB7200111), HN_ULONG_TO_UBASE(0x2178F283),
        HN_ULONG_TO_UBASE(0x48705A27), HN_ULONG_TO_UBASE(0xBD71855C),
        HN_ULONG_TO_UBASE(0xE797AA46), HN_ULONG_TO_UBASE(0x204B17EB),
        HN_ULONG_TO_UBASE(0x1A418C95), HN_ULONG_TO_UBASE(0x8200553C),
        HN_ULONG_TO_UBASE(0x7C5363E9), HN_ULONG_TO_UBASE(0x0AC39A6A),
        HN_ULONG_TO_UBASE(0x38E587EC), HN_ULONG_TO_UBASE(0x41E58BA1),
        HN_ULONG_TO_UBASE(0x00000092)
    },

    /* 20G.x */
    {
        HN_ULONG_TO_UBASE(0xD580C2BF), HN_ULONG_TO_UBASE(0x7A1128D3),
        HN_ULONG_TO_UBASE(0x33CB48FF), HN_ULONG_TO_UBASE(0x54CB80B9),
        HN_ULONG_TO_UBASE(0x124988A8), HN_ULONG_TO_UBASE(0x12E00E24),
        HN_ULONG_TO_UBASE(0x8973B0B6), HN_ULONG_TO_UBASE(0x2E41BD3E),
        HN_ULONG_TO_UBASE(0x0729F811), HN_ULONG_TO_UBASE(0x81F9B249),
        HN_ULONG_TO_UBASE(0x0B26F7D0), HN_ULONG_TO_UBASE(0x82B1E837),
        HN_ULONG_TO_UBASE(0x453D977E), HN_ULONG_TO_UBASE(0x66102520),
        HN_ULONG_TO_UBASE(0x76AF2FF3), HN_ULONG_TO_UBASE(0x86404FBF),
        HN_ULONG_TO_UBASE(0x00000122)
    },

    /* 20G.y */
    {
        HN_ULONG_TO_UBASE(0xDA706520), HN_ULONG_TO_UBASE(0x935EED87),
        HN_ULONG_TO_UBASE(0x72990FDC), HN_ULONG_TO_UBASE(0x35AEFF20),
        HN_ULONG_TO_UBASE(0x75F99B1D), HN_ULONG_TO_UBASE(0xF65CFB16),
        HN_ULONG_TO_UBASE(0x8369D686), HN_ULONG_TO_UBASE(0x0E6D254D),
        HN_ULONG_TO_UBASE(0x0882827E), HN_ULONG_TO_UBASE(0xC3493C5E),
        HN_ULONG_TO_UBASE(0x4F166119), HN_ULONG_TO_UBASE(0xA0EDF2DA),
        HN_ULONG_TO_UBASE(0x59363A25), HN_ULONG_TO_UBASE(0x7638D201),
        HN_ULONG_TO_UBASE(0x76088E6B), HN_ULONG_TO_UBASE(0xECE18042),
        HN_ULONG_TO_UBASE(0x00000138)
    },

    /* 21G.x */
    {
        HN_ULONG_TO_UBASE(0xF8913CC2), HN_ULONG_TO_UBASE(0x02FB999B),
        HN_ULONG_TO_UBASE(0xC961F4D7), HN_ULONG_TO_UBASE(0x81688347),
        HN_ULONG_TO_UBASE(0x8D0435C3), HN_ULONG_TO_UBASE(0xD54C91F7),
        HN_ULONG_TO_UBASE(0x990E6AB3), HN_ULONG_TO_UBASE(0xCBD0BCCA),
        HN_ULONG_TO_UBASE(0xD848398A), HN_ULONG_TO_UBASE(0x3C8BCE08),
        HN_ULONG_TO_UBASE(0xD5370ED6), HN_ULONG_TO_UBASE(0x83F3F979),
        HN_ULONG_TO_UBASE(0x74FD3AC3), HN_ULONG_TO_UBASE(0x7449BBD2),
        HN_ULONG_TO_UBASE(0x4E73CFAA), HN_ULONG_TO_UBASE(0xE6145DB8),
        HN_ULONG_TO_UBASE(0x0000015E)
    },

    /* 21G.y */
    {
        HN_ULONG_TO_UBASE(0x58044777), HN_ULONG_TO_UBASE(0x8A435E24),
        HN_ULONG_TO_UBASE(0x937E7687), HN_ULONG_TO_UBASE(0x29B9D279),
        HN_ULONG_TO_UBASE(0xE44D2874), HN_ULONG_TO_UBASE(0x42C2E303),
        HN_ULONG_TO_UBASE(0x9845F55F), HN_ULONG_TO_UBASE(0xFDE623CA),
        HN_ULONG_TO_UBASE(0x2FA1BC3E), HN_ULONG_TO_UBASE(0x752C258F),
        HN_ULONG_TO_UBASE(0xCE8A096A), HN_ULONG_TO_UBASE(0x198DB2B3),
        HN_ULONG_TO_UBASE(0x6F2EC50F), HN_ULONG_TO_UBASE(0x59B5361B),
        HN_ULONG_TO_UBASE(0x1A2AA43F), HN_ULONG_TO_UBASE(0x5A4F209C),
        HN_ULONG_TO_UBASE(0x0000014A)
    },

    /* 22G.x */
    {
        HN_ULONG_TO_UBASE(0x4F005DA0), HN_ULONG_TO_UBASE(0xFCBB48B5),
        HN_ULONG_TO_UBASE(0x50CBC0C5), HN_ULONG_TO_UBASE(0x2A8D3120),
        HN_ULONG_TO_UBASE(0xE9AC811D), HN_ULONG_TO_UBASE(0xB33C1526),
        HN_ULONG_TO_UBASE(0x586013F2), HN_ULONG_TO_UBASE(0x91E493CE),
        HN_ULONG_TO_UBASE(0x488D1688), HN_ULONG_TO_UBASE(0x99412264),
        HN_ULONG_TO_UBASE(0x875C45E1), HN_ULONG_TO_UBASE(0x7966C1C1),
        HN_ULONG_TO_UBASE(0xBE0D5B72), HN_ULONG_TO_UBASE(0x9DBC86D2),
        HN_ULONG_TO_UBASE(0x1553D029), HN_ULONG_TO_UBASE(0x63A83498),
        HN_ULONG_TO_UBASE(0x00000068)
    },

    /* 22G.y */
    {
        HN_ULONG_TO_UBASE(0x9D44055B), HN_ULONG_TO_UBASE(0x184F3300),
        HN_ULONG_TO_UBASE(0xD232A2F5), HN_ULONG_TO_UBASE(0xADBEC2BB),
        HN_ULONG_TO_UBASE(0x5A2A5EB5), HN_ULONG_TO_UBASE(0x4E0B32A8),
        HN_ULONG_TO_UBASE(0xE3535F4C), HN_ULONG_TO_UBASE(0x938D776C),
        HN_ULONG_TO_UBASE(0x0F169CF8), HN_ULONG_TO_UBASE(0xD2591A1A),
        HN_ULONG_TO_UBASE(0xE7A2093D), HN_ULONG_TO_UBASE(0x8DCA29F4),
        HN_ULONG_TO_UBASE(0x145F902B), HN_ULONG_TO_UBASE(0xB8746330),
        HN_ULONG_TO_UBASE(0xFE2CEBEC), HN_ULONG_TO_UBASE(0x816D6F86),
        HN_ULONG_TO_UBASE(0x00000068)
    },

    /* 23G.x */
    {
        HN_ULONG_TO_UBASE(0x2A7FABD6), HN_ULONG_TO_UBASE(0x5B774088),
        HN_ULONG_TO_UBASE(0x826F236E), HN_ULONG_TO_UBASE(0x0911E7F0),
        HN_ULONG_TO_UBASE(0x84F0BBD3), HN_ULONG_TO_UBASE(0xBC4D014C),
        HN_ULONG_TO_UBASE(0x2E000C1A), HN_ULONG_TO_UBASE(0x0B8BCD8F),
        HN_ULONG_TO_UBASE(0x2DDEB721), HN_ULONG_TO_UBASE(0xD5FF7778),
        HN_ULONG_TO_UBASE(0x7EF826A9), HN_ULONG_TO_UBASE(0x0F7A76F9),
        HN_ULONG_TO_UBASE(0x644D2F94), HN_ULONG_TO_UBASE(0x031CE26F),
        HN_ULONG_TO_UBASE(0x78FDC7ED), HN_ULONG_TO_UBASE(0x1EAFDC82),
        HN_ULONG_TO_UBASE(0x000001E2)
    },

    /* 23G.y */
    {
        HN_ULONG_TO_UBASE(0xA60BC2A1), HN_ULONG_TO_UBASE(0x16BF1952),
        HN_ULONG_TO_UBASE(0x3DD7DCD5), HN_ULONG_TO_UBASE(0x2CE4110C),
        HN_ULONG_TO_UBASE(0x78E260C3), HN_ULONG_TO_UBASE(0x3DF02BF1),
        HN_ULONG_TO_UBASE(0x3944B29C), HN_ULONG_TO_UBASE(0x132208C2),
        HN_ULONG_TO_UBASE(0x59682E01), HN_ULONG_TO_UBASE(0x13ADD246),
        HN_ULONG_TO_UBASE(0x133B08C5), HN_ULONG_TO_UBASE(0x87922B46),
        HN_ULONG_TO_UBASE(0x51C7AEA4), HN_ULONG_TO_UBASE(0x5B3E4932),
        HN_ULONG_TO_UBASE(0x9380BDFF), HN_ULONG_TO_UBASE(0x9E77C83B),
        HN_ULONG_TO_UBASE(0x000000E0)
    },

    /* 24G.x */
    {
        HN_ULONG_TO_UBASE(0x54746586), HN_ULONG_TO_UBASE(0x3CE44040),
        HN_ULONG_TO_UBASE(0x31E2CB31), HN_ULONG_TO_UBASE(0x5260F1BE),
        HN_ULONG_TO_UBASE(0x1C0A1DC6), HN_ULONG_TO_UBASE(0x802E6890),
        HN_ULONG_TO_UBASE(0xF84363BB), HN_ULONG_TO_UBASE(0x5E9AAEA8),
        HN_ULONG_TO_UBASE(0x4CCCCA39), HN_ULONG_TO_UBASE(0xFFD8A962),
        HN_ULONG_TO_UBASE(0xFA9EF6E4), HN_ULONG_TO_UBASE(0xE12A8BE6),
        HN_ULONG_TO_UBASE(0x31BE868F), HN_ULONG_TO_UBASE(0x85338185),
        HN_ULONG_TO_UBASE(0xA23A3B36), HN_ULONG_TO_UBASE(0x25878406),
        HN_ULONG_TO_UBASE(0x0000003C)
    },

    /* 24G.y */
    {
        HN_ULONG_TO_UBASE(0x96E54499), HN_ULONG_TO_UBASE(0x14827414),
        HN_ULONG_TO_UBASE(0x8E050034), HN_ULONG_TO_UBASE(0xA0732F96),
        HN_ULONG_TO_UBASE(0x467CADC7), HN_ULONG_TO_UBASE(0x1C6FA676),
        HN_ULONG_TO_UBASE(0x98D6927C), HN_ULONG_TO_UBASE(0xA63BA10C),
        HN_ULONG_TO_UBASE(0xDCC2500F), HN_ULONG_TO_UBASE(0x58CB6F96),
        HN_ULONG_TO_UBASE(0xEA4FD9C5), HN_ULONG_TO_UBASE(0xACCB76D6),
        HN_ULONG_TO_UBASE(0x30786990), HN_ULONG_TO_UBASE(0xE2A2F59B),
        HN_ULONG_TO_UBASE(0xD224CCFE), HN_ULONG_TO_UBASE(0xD8B24679),
        HN_ULONG_TO_UBASE(0x000001B2)
    },

    /* 25G.x */
    {
        HN_ULONG_TO_UBASE(0xA84D476C), HN_ULONG_TO_UBASE(0xDBE7BC43),
        HN_ULONG_TO_UBASE(0x0DF0F82F), HN_ULONG_TO_UBASE(0x80C66135),
        HN_ULONG_TO_UBASE(0x7996B3DD), HN_ULONG_TO_UBASE(0xFDA3915E),
        HN_ULONG_TO_UBASE(0x5460B6A5), HN_ULONG_TO_UBASE(0xD309856F),
        HN_ULONG_TO_UBASE(0xFB5F2A03), HN_ULONG_TO_UBASE(0xDA60ECFB),
        HN_ULONG_TO_UBASE(0x87F5C81E), HN_ULONG_TO_UBASE(0xB45421CC),
        HN_ULONG_TO_UBASE(0x93297E69), HN_ULONG_TO_UBASE(0xE78F50B7),
        HN_ULONG_TO_UBASE(0x92B7AB1B), HN_ULONG_TO_UBASE(0xB57EF6E0),
        HN_ULONG_TO_UBASE(0x00000020)
    },

    /* 25G.y */
    {
        HN_ULONG_TO_UBASE(0xD5A7E947), HN_ULONG_TO_UBASE(0x2FAC246B),
        HN_ULONG_TO_UBASE(0x3C2C0E9E), HN_ULONG_TO_UBASE(0xA3560F17),
        HN_ULONG_TO_UBASE(0x99A15EDC), HN_ULONG_TO_UBASE(0x36F3BEA7),
        HN_ULONG_TO_UBASE(0xF6DF7626), HN_ULONG_TO_UBASE(0x51E0953F),
        HN_ULONG_TO_UBASE(0xE181410F), HN_ULONG_TO_UBASE(0x791FB8F1),
        HN_ULONG_TO_UBASE(0x065B1CD7), HN_ULONG_TO_UBASE(0x9E592427),
        HN_ULONG_TO_UBASE(0x6E9A54B5), HN_ULONG_TO_UBASE(0x492C7736),
        HN_ULONG_TO_UBASE(0xF664EAD5), HN_ULONG_TO_UBASE(0x8F186347),
        HN_ULONG_TO_UBASE(0x000001DE)
    },

    /* 26G.x */
    {
        HN_ULONG_TO_UBASE(0xCC9F295B), HN_ULONG_TO_UBASE(0xF8B25DE3),
        HN_ULONG_TO_UBASE(0xD481F758), HN_ULONG_TO_UBASE(0x6A3A6AFD),
        HN_ULONG_TO_UBASE(0xD7194DD7), HN_ULONG_TO_UBASE(0x0F362D65),
        HN_ULONG_TO_UBASE(0x72839C8B), HN_ULONG_TO_UBASE(0xD0706B14),
        HN_ULONG_TO_UBASE(0x86E79D73), HN_ULONG_TO_UBASE(0x863FC677),
        HN_ULONG_TO_UBASE(0xBD51AA66), HN_ULONG_TO_UBASE(0x7A312DD4),
        HN_ULONG_TO_UBASE(0xE2FB1520), HN_ULONG_TO_UBASE(0x4F54148B),
        HN_ULONG_TO_UBASE(0xD3FAFAA8), HN_ULONG_TO_UBASE(0xF819350E),
        HN_ULONG_TO_UBASE(0x000000A5)
    },

    /* 26G.y */
    {
        HN_ULONG_TO_UBASE(0xD0BA1319), HN_ULONG_TO_UBASE(0xDADE2B22),
        HN_ULONG_TO_UBASE(0x0E128DE4), HN_ULONG_TO_UBASE(0xEC0DF712),
        HN_ULONG_TO_UBASE(0xA47D34B8), HN_ULONG_TO_UBASE(0xBFBB4A30),
        HN_ULONG_TO_UBASE(0x6C0FE4B4), HN_ULONG_TO_UBASE(0x27A996FE),
        HN_ULONG_TO_UBASE(0xFC390C4C), HN_ULONG_TO_UBASE(0xF37AC376),
        HN_ULONG_TO_UBASE(0x8F1D9559), HN_ULONG_TO_UBASE(0xA28F1992),
        HN_ULONG_TO_UBASE(0x7917FB9E), HN_ULONG_TO_UBASE(0x59A81149),
        HN_ULONG_TO_UBASE(0x24E5BB5E), HN_ULONG_TO_UBASE(0x5658B74F),
        HN_ULONG_TO_UBASE(0x000001C1)
    },

    /* 27G.x */
    {
        HN_ULONG_TO_UBASE(0xE090CB47), HN_ULONG_TO_UBASE(0x0B8C54ED),
        HN_ULONG_TO_UBASE(0xABACC4B9), HN_ULONG_TO_UBASE(0xDDCEF543),
        HN_ULONG_TO_UBASE(0x6F45D144), HN_ULONG_TO_UBASE(0xAA5D9F95),
        HN_ULONG_TO_UBASE(0x6D5B0B7D), HN_ULONG_TO_UBASE(0x5829EA88),
        HN_ULONG_TO_UBASE(0x5D9B5A9B), HN_ULONG_TO_UBASE(0xA08415C0),
        HN_ULONG_TO_UBASE(0x9D469207), HN_ULONG_TO_UBASE(0x0DCDD858),
        HN_ULONG_TO_UBASE(0xAD47D576), HN_ULONG_TO_UBASE(0x91EF5090),
        HN_ULONG_TO_UBASE(0xFC9C5403), HN_ULONG_TO_UBASE(0x286B59F7),
        HN_ULONG_TO_UBASE(0x000000A4)
    },

    /* 27G.y */
    {
        HN_ULONG_TO_UBASE(0x681A6033), HN_ULONG_TO_UBASE(0x51F28B4E),
        HN_ULONG_TO_UBASE(0x5586A02D), HN_ULONG_TO_UBASE(0x92C8812F),
        HN_ULONG_TO_UBASE(0x37F75BE9), HN_ULONG_TO_UBASE(0x65497752),
        HN_ULONG_TO_UBASE(0xE465AC3D), HN_ULONG_TO_UBASE(0x39ED9C48),
        HN_ULONG_TO_UBASE(0xEA5746A3), HN_ULONG_TO_UBASE(0x52B0558D),
        HN_ULONG_TO_UBASE(0x2281BF57), HN_ULONG_TO_UBASE(0xB1AD72EA),
        HN_ULONG_TO_UBASE(0xC29BC5BA), HN_ULONG_TO_UBASE(0xFA48BE23),
        HN_ULONG_TO_UBASE(0xB11E0B1A), HN_ULONG_TO_UBASE(0x5DDD7E6B),
        HN_ULONG_TO_UBASE(0x00000144)
    },

    /* 28G.x */
    {
        HN_ULONG_TO_UBASE(0xDFBF1C5D), HN_ULONG_TO_UBASE(0x6B91BD8F),
        HN_ULONG_TO_UBASE(0x35CBBE74), HN_ULONG_TO_UBASE(0x29CBC8C5),
        HN_ULONG_TO_UBASE(0xECA1F1E8), HN_ULONG_TO_UBASE(0xF2BF1BF0),
        HN_ULONG_TO_UBASE(0x8B74129C), HN_ULONG_TO_UBASE(0x64BC1B61),
        HN_ULONG_TO_UBASE(0xA26DB0BA), HN_ULONG_TO_UBASE(0x5701D92D),
        HN_ULONG_TO_UBASE(0x629C49B0), HN_ULONG_TO_UBASE(0x3BBBDA1D),
        HN_ULONG_TO_UBASE(0x628F9CF9), HN_ULONG_TO_UBASE(0x77932B00),
        HN_ULONG_TO_UBASE(0xE3B93FD6), HN_ULONG_TO_UBASE(0xF4DD2F98),
        HN_ULONG_TO_UBASE(0x0000004E)
    },

    /* 28G.y */
    {
        HN_ULONG_TO_UBASE(0xE84D1AA9), HN_ULONG_TO_UBASE(0x50D3F239),
        HN_ULONG_TO_UBASE(0x80BE7733), HN_ULONG_TO_UBASE(0x76243D29),
        HN_ULONG_TO_UBASE(0x5F3A7F3E), HN_ULONG_TO_UBASE(0x8F1F1050),
        HN_ULONG_TO_UBASE(0xFCEF3C41), HN_ULONG_TO_UBASE(0x5B49D4A6),
        HN_ULONG_TO_UBASE(0x15608CF3), HN_ULONG_TO_UBASE(0x97025D37),
        HN_ULONG_TO_UBASE(0x0ADCFFE2), HN_ULONG_TO_UBASE(0x8CE7FBDA),
        HN_ULONG_TO_UBASE(0xF8EFC79A), HN_ULONG_TO_UBASE(0xDB4849EC),
        HN_ULONG_TO_UBASE(0x67855D5B), HN_ULONG_TO_UBASE(0xFE454312),
        HN_ULONG_TO_UBASE(0x00000087)
    },

    /* 29G.x */
    {
        HN_ULONG_TO_UBASE(0x643FA4EF), HN_ULONG_TO_UBASE(0xC6D4508A),
        HN_ULONG_TO_UBASE(0x2F666F82), HN_ULONG_TO_UBASE(0xA54E8CC0),
        HN_ULONG_TO_UBASE(0x2DC798A4), HN_ULONG_TO_UBASE(0x34A01969),
        HN_ULONG_TO_UBASE(0x111EBEC5), HN_ULONG_TO_UBASE(0x3B92FC56),
        HN_ULONG_TO_UBASE(0xCE2FEDD7), HN_ULONG_TO_UBASE(0x8F6BDC34),
        HN_ULONG_TO_UBASE(0x57CC1DC0), HN_ULONG_TO_UBASE(0x9D5D1B75),
        HN_ULONG_TO_UBASE(0xB8FEF3F8), HN_ULONG_TO_UBASE(0x8019D044),
        HN_ULONG_TO_UBASE(0xA5F3C3DA), HN_ULONG_TO_UBASE(0x8CB35753),
        HN_ULONG_TO_UBASE(0x00000091)
    },

    /* 29G.y */
    {
        HN_ULONG_TO_UBASE(0x1D608111), HN_ULONG_TO_UBASE(0x81975CB9),
        HN_ULONG_TO_UBASE(0x6D5131E8), HN_ULONG_TO_UBASE(0x423B14C7),
        HN_ULONG_TO_UBASE(0x9822E028), HN_ULONG_TO_UBASE(0xCD872107),
        HN_ULONG_TO_UBASE(0x55997E16), HN_ULONG_TO_UBASE(0x3B325E7E),
        HN_ULONG_TO_UBASE(0x77CB3C94), HN_ULONG_TO_UBASE(0x076A9D7A),
        HN_ULONG_TO_UBASE(0xA0038852), HN_ULONG_TO_UBASE(0xBC1550AA),
        HN_ULONG_TO_UBASE(0xF47B925F), HN_ULONG_TO_UBASE(0x4F0E3B8E),
        HN_ULONG_TO_UBASE(0xDAA979F8), HN_ULONG_TO_UBASE(0xEFD3DA16),
        HN_ULONG_TO_UBASE(0x00000117)
    },

    /* 30G.x */
    {
        HN_ULONG_TO_UBASE(0x8D320182), HN_ULONG_TO_UBASE(0x39D27F3B),
        HN_ULONG_TO_UBASE(0x57F13AD2), HN_ULONG_TO_UBASE(0xAF725A25),
        HN_ULONG_TO_UBASE(0x21F64D54), HN_ULONG_TO_UBASE(0x776680A4),
        HN_ULONG_TO_UBASE(0x346AD04E), HN_ULONG_TO_UBASE(0x8C76F109),
        HN_ULONG_TO_UBASE(0x02313957), HN_ULONG_TO_UBASE(0x1C57732C),
        HN_ULONG_TO_UBASE(0x0026D082), HN_ULONG_TO_UBASE(0xD610C412),
        HN_ULONG_TO_UBASE(0x9F070119), HN_ULONG_TO_UBASE(0xBF2AFD03),
        HN_ULONG_TO_UBASE(0x0FE119E2), HN_ULONG_TO_UBASE(0xB78AB112),
        HN_ULONG_TO_UBASE(0x00000195)
    },

    /* 30G.y */
    {
        HN_ULONG_TO_UBASE(0xDE80E59F), HN_ULONG_TO_UBASE(0x6759CF0F),
        HN_ULONG_TO_UBASE(0x1C569A55), HN_ULONG_TO_UBASE(0xBC9C518D),
        HN_ULONG_TO_UBASE(0x6CCC33E7), HN_ULONG_TO_UBASE(0x9EF3AFB2),
        HN_ULONG_TO_UBASE(0x3B2B65A2), HN_ULONG_TO_UBASE(0x54CD7E1C),
        HN_ULONG_TO_UBASE(0x89020840), HN_ULONG_TO_UBASE(0x056D2549),
        HN_ULONG_TO_UBASE(0xEA20691F), HN_ULONG_TO_UBASE(0x2B8C3C49),
        HN_ULONG_TO_UBASE(0xECF9ED3D), HN_ULONG_TO_UBASE(0xDD6F6CAD),
        HN_ULONG_TO_UBASE(0xCD81C6B2), HN_ULONG_TO_UBASE(0x0287ED9E),
        HN_ULONG_TO_UBASE(0x000001AA)
    },

    /* 31G.x */
    {
        HN_ULONG_TO_UBASE(0x0886FCE5), HN_ULONG_TO_UBASE(0x0932A85E),
        HN_ULONG_TO_UBASE(0x539E0749), HN_ULONG_TO_UBASE(0xF22990FC),
        HN_ULONG_TO_UBASE(0x9622B480), HN_ULONG_TO_UBASE(0x0900525D),
        HN_ULONG_TO_UBASE(0x2322A79E), HN_ULONG_TO_UBASE(0xF8159FB8),
        HN_ULONG_TO_UBASE(0x52225E4A), HN_ULONG_TO_UBASE(0x16BC8FBA),
        HN_ULONG_TO_UBASE(0x3A8B6083), HN_ULONG_TO_UBASE(0x80AED84F),
        HN_ULONG_TO_UBASE(0x7C8B52D4), HN_ULONG_TO_UBASE(0x0EC2EA9E),
        HN_ULONG_TO_UBASE(0x8C474025), HN_ULONG_TO_UBASE(0x88586280),
        HN_ULONG_TO_UBASE(0x00000122)
    },

    /* 31G.y */
    {
        HN_ULONG_TO_UBASE(0x1FA537FC), HN_ULONG_TO_UBASE(0x7E82B98A),
        HN_ULONG_TO_UBASE(0xB07AEE91), HN_ULONG_TO_UBASE(0x06814D94),
        HN_ULONG_TO_UBASE(0x39BBF49E), HN_ULONG_TO_UBASE(0x2ACE89A4),
        HN_ULONG_TO_UBASE(0x572F35DE), HN_ULONG_TO_UBASE(0x4272B632),
        HN_ULONG_TO_UBASE(0xA6132D49), HN_ULONG_TO_UBASE(0x4AA5EC9C),
        HN_ULONG_TO_UBASE(0x0AC0CC3E), HN_ULONG_TO_UBASE(0x6DA8505B),
        HN_ULONG_TO_UBASE(0xF0B72ACE), HN_ULONG_TO_UBASE(0xE50B950F),
        HN_ULONG_TO_UBASE(0xEB7A6DCB), HN_ULONG_TO_UBASE(0x38D4E07D),
        HN_ULONG_TO_UBASE(0x000000BB)
    }
};
static NX_CRYPTO_CONST HN_UBASE           secp521r1_fixed_points_2e_data[][68 >> HN_SIZE_SHIFT] =
{

    /* 2^e * 1G.x */
    {
        HN_ULONG_TO_UBASE(0x676A875A), HN_ULONG_TO_UBASE(0xB19A5828),
        HN_ULONG_TO_UBASE(0xC83A4911), HN_ULONG_TO_UBASE(0x40729F28),
        HN_ULONG_TO_UBASE(0xC6DCE05B), HN_ULONG_TO_UBASE(0x511949B1),
        HN_ULONG_TO_UBASE(0x3FCF0490), HN_ULONG_TO_UBASE(0x25914C8C),
        HN_ULONG_TO_UBASE(0x0C6C2742), HN_ULONG_TO_UBASE(0xC49490DE),
        HN_ULONG_TO_UBASE(0xDF6CD770), HN_ULONG_TO_UBASE(0x3B21FEEF),
        HN_ULONG_TO_UBASE(0xDD1A82A2), HN_ULONG_TO_UBASE(0x012D2802),
        HN_ULONG_TO_UBASE(0x6453439D), HN_ULONG_TO_UBASE(0x72F5A0B7),
        HN_ULONG_TO_UBASE(0x00000020)
    },

    /* 2^e * 1G.y */
    {
        HN_ULONG_TO_UBASE(0x9B58E26D), HN_ULONG_TO_UBASE(0x3E85D07D),
        HN_ULONG_TO_UBASE(0x1C50AF24), HN_ULONG_TO_UBASE(0x56527BDD),
        HN_ULONG_TO_UBASE(0x7DF377D0), HN_ULONG_TO_UBASE(0x564B5658),
        HN_ULONG_TO_UBASE(0x65E97A30), HN_ULONG_TO_UBASE(0xF5B03358),
        HN_ULONG_TO_UBASE(0x6B31BC52), HN_ULONG_TO_UBASE(0x2B22D6E9),
        HN_ULONG_TO_UBASE(0xCCCD4BBA), HN_ULONG_TO_UBASE(0x8AD449AB),
        HN_ULONG_TO_UBASE(0xF7C162A2), HN_ULONG_TO_UBASE(0x84B8B888),
        HN_ULONG_TO_UBASE(0x4D56F29B), HN_ULONG_TO_UBASE(0x3B647F0E),
        HN_ULONG_TO_UBASE(0x000001FB)
    },

    /* 2^e * 2G.x */
    {
        HN_ULONG_TO_UBASE(0x9CAFE97E), HN_ULONG_TO_UBASE(0x686698E8),
        HN_ULONG_TO_UBASE(0xB6891510), HN_ULONG_TO_UBASE(0x3F2A465D),
        HN_ULONG_TO_UBASE(0xC9AAD498), HN_ULONG_TO_UBASE(0xA99BCD91),
        HN_ULONG_TO_UBASE(0xA47C770B), HN_ULONG_TO_UBASE(0x0C7AE217),
        HN_ULONG_TO_UBASE(0x6A5E6F06), HN_ULONG_TO_UBASE(0x95067A8B),
        HN_ULONG_TO_UBASE(0x25A11429), HN_ULONG_TO_UBASE(0x0B55DA40),
        HN_ULONG_TO_UBASE(0x997CABFF), HN_ULONG_TO_UBASE(0x5372B805),
        HN_ULONG_TO_UBASE(0x963D233F), HN_ULONG_TO_UBASE(0x1E8BF414),
        HN_ULONG_TO_UBASE(0x0000002B)
    },

    /* 2^e * 2G.y */
    {
        HN_ULONG_TO_UBASE(0xAB613409), HN_ULONG_TO_UBASE(0x1343EBE6),
        HN_ULONG_TO_UBASE(0xAC96CD26), HN_ULONG_TO_UBASE(0x7AAB5A01),
        HN_ULONG_TO_UBASE(0x112C0B65), HN_ULONG_TO_UBASE(0x243758AF),
        HN_ULONG_TO_UBASE(0xB236CAD6), HN_ULONG_TO_UBASE(0xD276E441),
        HN_ULONG_TO_UBASE(0x557111DA), HN_ULONG_TO_UBASE(0x88A7D43F),
        HN_ULONG_TO_UBASE(0xA0F46068), HN_ULONG_TO_UBASE(0xC6DACE8B),
        HN_ULONG_TO_UBASE(0xCF4A5CEB), HN_ULONG_TO_UBASE(0x866C31EF),
        HN_ULONG_TO_UBASE(0x9554F30C), HN_ULONG_TO_UBASE(0x6DD991B8),
        HN_ULONG_TO_UBASE(0x00000066)
    },

    /* 2^e * 3G.x */
    {
        HN_ULONG_TO_UBASE(0xD3E5BCDA), HN_ULONG_TO_UBASE(0x542A7180),
        HN_ULONG_TO_UBASE(0x7A94EFCB), HN_ULONG_TO_UBASE(0x4AF33286),
        HN_ULONG_TO_UBASE(0x17AB6AE0), HN_ULONG_TO_UBASE(0x7B9B88E1),
        HN_ULONG_TO_UBASE(0x040672E3), HN_ULONG_TO_UBASE(0x39D62FAB),
        HN_ULONG_TO_UBASE(0x32FEB463), HN_ULONG_TO_UBASE(0x41F41B8A),
        HN_ULONG_TO_UBASE(0x0A699C83), HN_ULONG_TO_UBASE(0x0268C13E),
        HN_ULONG_TO_UBASE(0x3B3B6CD8), HN_ULONG_TO_UBASE(0x111976EA),
        HN_ULONG_TO_UBASE(0x404660C9), HN_ULONG_TO_UBASE(0x5CFB4C1D),
        HN_ULONG_TO_UBASE(0x000000FB)
    },

    /* 2^e * 3G.y */
    {
        HN_ULONG_TO_UBASE(0x6810A938), HN_ULONG_TO_UBASE(0x87E514CE),
        HN_ULONG_TO_UBASE(0x48BA41A4), HN_ULONG_TO_UBASE(0x65A8EA95),
        HN_ULONG_TO_UBASE(0x3B052CBF), HN_ULONG_TO_UBASE(0xAAE6A840),
        HN_ULONG_TO_UBASE(0x7DEB11C0), HN_ULONG_TO_UBASE(0xD470CDFF),
        HN_ULONG_TO_UBASE(0x822A18AF), HN_ULONG_TO_UBASE(0xC3531E99),
        HN_ULONG_TO_UBASE(0x1428CB49), HN_ULONG_TO_UBASE(0xD53F05C1),
        HN_ULONG_TO_UBASE(0xB6E4EDA7), HN_ULONG_TO_UBASE(0xF2217457),
        HN_ULONG_TO_UBASE(0x4561F140), HN_ULONG_TO_UBASE(0x92F3A027),
        HN_ULONG_TO_UBASE(0x000000A8)
    },

    /* 2^e * 4G.x */
    {
        HN_ULONG_TO_UBASE(0x4876BAB8), HN_ULONG_TO_UBASE(0xC68052C9),
        HN_ULONG_TO_UBASE(0xF84C2E38), HN_ULONG_TO_UBASE(0x81F704BA),
        HN_ULONG_TO_UBASE(0xF2CF9927), HN_ULONG_TO_UBASE(0x28BB9436),
        HN_ULONG_TO_UBASE(0xD06C972C), HN_ULONG_TO_UBASE(0xB89BBFA0),
        HN_ULONG_TO_UBASE(0xB7A78E76), HN_ULONG_TO_UBASE(0x00FE1DD8),
        HN_ULONG_TO_UBASE(0xF7F1E1AA), HN_ULONG_TO_UBASE(0x3ADFB05A),
        HN_ULONG_TO_UBASE(0xB7278EEE), HN_ULONG_TO_UBASE(0x45321E42),
        HN_ULONG_TO_UBASE(0xA528EBE4), HN_ULONG_TO_UBASE(0x13D850E0),
        HN_ULONG_TO_UBASE(0x0000006F)
    },

    /* 2^e * 4G.y */
    {
        HN_ULONG_TO_UBASE(0x1934143A), HN_ULONG_TO_UBASE(0x6B724C68),
        HN_ULONG_TO_UBASE(0xB657D809), HN_ULONG_TO_UBASE(0x2F944AFD),
        HN_ULONG_TO_UBASE(0xABA2FA08), HN_ULONG_TO_UBASE(0x38E501DF),
        HN_ULONG_TO_UBASE(0xBE66E568), HN_ULONG_TO_UBASE(0x9285ECE1),
        HN_ULONG_TO_UBASE(0xF9DF3327), HN_ULONG_TO_UBASE(0x7AF221F7),
        HN_ULONG_TO_UBASE(0xDE74AEFC), HN_ULONG_TO_UBASE(0x00B8A86F),
        HN_ULONG_TO_UBASE(0x9A992072), HN_ULONG_TO_UBASE(0x19D67518),
        HN_ULONG_TO_UBASE(0xD9DDBBB6), HN_ULONG_TO_UBASE(0x806D1C88),
        HN_ULONG_TO_UBASE(0x0000009B)
    },

    /* 2^e * 5G.x */
    {
        HN_ULONG_TO_UBASE(0x69223216), HN_ULONG_TO_UBASE(0x903F9E4D),
        HN_ULONG_TO_UBASE(0x605F145F), HN_ULONG_TO_UBASE(0x27CA08BA),
        HN_ULONG_TO_UBASE(0xCE206603), HN_ULONG_TO_UBASE(0xA7806921),
        HN_ULONG_TO_UBASE(0xE613A7B5), HN_ULONG_TO_UBASE(0x605F63EA),
        HN_ULONG_TO_UBASE(0xAEE0B687), HN_ULONG_TO_UBASE(0xC9522A1A),
        HN_ULONG_TO_UBASE(0xA0B479D4), HN_ULONG_TO_UBASE(0xA3FEBB3F),
        HN_ULONG_TO_UBASE(0x63C1DC7E), HN_ULONG_TO_UBASE(0x4E4C7434),
        HN_ULONG_TO_UBASE(0x3BE76DB6), HN_ULONG_TO_UBASE(0xF25C30D1),
        HN_ULONG_TO_UBASE(0x0000011B)
    },

    /* 2^e * 5G.y */
    {
        HN_ULONG_TO_UBASE(0x34782D9F), HN_ULONG_TO_UBASE(0x0C5E62AF),
        HN_ULONG_TO_UBASE(0xD72C5FF7), HN_ULONG_TO_UBASE(0xD7820151),
        HN_ULONG_TO_UBASE(0x6803C198), HN_ULONG_TO_UBASE(0xD373F830),
        HN_ULONG_TO_UBASE(0x4A7D6949), HN_ULONG_TO_UBASE(0x0130F180),
        HN_ULONG_TO_UBASE(0x45F00A89), HN_ULONG_TO_UBASE(0x3D1806E1),
        HN_ULONG_TO_UBASE(0x9141560F), HN_ULONG_TO_UBASE(0x34452841),
        HN_ULONG_TO_UBASE(0x1202463D), HN_ULONG_TO_UBASE(0xEC6077CF),
        HN_ULONG_TO_UBASE(0x78B546B9), HN_ULONG_TO_UBASE(0xC8FECC41),
        HN_ULONG_TO_UBASE(0x0000009D)
    },

    /* 2^e * 6G.x */
    {
        HN_ULONG_TO_UBASE(0x5CBA8E1C), HN_ULONG_TO_UBASE(0x1C8C8F82),
        HN_ULONG_TO_UBASE(0xD444D2D8), HN_ULONG_TO_UBASE(0xA591AB99),
        HN_ULONG_TO_UBASE(0xD33871C0), HN_ULONG_TO_UBASE(0xA61764E5),
        HN_ULONG_TO_UBASE(0x3DF6159A), HN_ULONG_TO_UBASE(0x53C89BEC),
        HN_ULONG_TO_UBASE(0x43E938E7), HN_ULONG_TO_UBASE(0x27B258B6),
        HN_ULONG_TO_UBASE(0xDCAB4FDB), HN_ULONG_TO_UBASE(0x82AE0C0A),
        HN_ULONG_TO_UBASE(0xE5690F75), HN_ULONG_TO_UBASE(0x7265C1A3),
        HN_ULONG_TO_UBASE(0x9D05D099), HN_ULONG_TO_UBASE(0xAEFDF1FC),
        HN_ULONG_TO_UBASE(0x00000084)
    },

    /* 2^e * 6G.y */
    {
        HN_ULONG_TO_UBASE(0xA25FE19D), HN_ULONG_TO_UBASE(0x1D3A011F),
        HN_ULONG_TO_UBASE(0x56E437F8), HN_ULONG_TO_UBASE(0x6173D09C),
        HN_ULONG_TO_UBASE(0x17C1F149), HN_ULONG_TO_UBASE(0x139B51BB),
        HN_ULONG_TO_UBASE(0x610A2561), HN_ULONG_TO_UBASE(0x15F39F5A),
        HN_ULONG_TO_UBASE(0x9C5FFCF7), HN_ULONG_TO_UBASE(0x83860303),
        HN_ULONG_TO_UBASE(0xADACF2EE), HN_ULONG_TO_UBASE(0x1D7B83D0),
        HN_ULONG_TO_UBASE(0xEFB9E833), HN_ULONG_TO_UBASE(0xC947072C),
        HN_ULONG_TO_UBASE(0x9E785B57), HN_ULONG_TO_UBASE(0xDCFAB8C0),
        HN_ULONG_TO_UBASE(0x00000028)
    },

    /* 2^e * 7G.x */
    {
        HN_ULONG_TO_UBASE(0xA14A0C3B), HN_ULONG_TO_UBASE(0xC8AE34DA),
        HN_ULONG_TO_UBASE(0x360B4741), HN_ULONG_TO_UBASE(0xBF6251D0),
        HN_ULONG_TO_UBASE(0x3AA611C7), HN_ULONG_TO_UBASE(0x4DA0E31F),
        HN_ULONG_TO_UBASE(0x043332E7), HN_ULONG_TO_UBASE(0xCED6DAB0),
        HN_ULONG_TO_UBASE(0xA7202A78), HN_ULONG_TO_UBASE(0x6EA39A17),
        HN_ULONG_TO_UBASE(0x72A1A2A0), HN_ULONG_TO_UBASE(0x8166A916),
        HN_ULONG_TO_UBASE(0x9E01EFA7), HN_ULONG_TO_UBASE(0x885466C5),
        HN_ULONG_TO_UBASE(0x2F36F714), HN_ULONG_TO_UBASE(0x86F2D088),
        HN_ULONG_TO_UBASE(0x00000048)
    },

    /* 2^e * 7G.y */
    {
        HN_ULONG_TO_UBASE(0x0B9C62F8), HN_ULONG_TO_UBASE(0x10A8AB6A),
        HN_ULONG_TO_UBASE(0x9F1A0FCA), HN_ULONG_TO_UBASE(0xE2DC4728),
        HN_ULONG_TO_UBASE(0x131F3E96), HN_ULONG_TO_UBASE(0x6D685928),
        HN_ULONG_TO_UBASE(0x5EF9C6BE), HN_ULONG_TO_UBASE(0x5593F3DE),
        HN_ULONG_TO_UBASE(0xFDA508D5), HN_ULONG_TO_UBASE(0x28A1C653),
        HN_ULONG_TO_UBASE(0xEEF18BF0), HN_ULONG_TO_UBASE(0x8D202E40),
        HN_ULONG_TO_UBASE(0xEB6017C8), HN_ULONG_TO_UBASE(0xA67A3238),
        HN_ULONG_TO_UBASE(0x302DECD2), HN_ULONG_TO_UBASE(0xDAAF32B6),
        HN_ULONG_TO_UBASE(0x000000C0)
    },

    /* 2^e * 8G.x */
    {
        HN_ULONG_TO_UBASE(0x75804DA9), HN_ULONG_TO_UBASE(0x2A7E8AC1),
        HN_ULONG_TO_UBASE(0x8D50BDC6), HN_ULONG_TO_UBASE(0x0B41F611),
        HN_ULONG_TO_UBASE(0xD8FAFB1C), HN_ULONG_TO_UBASE(0xCFA0A757),
        HN_ULONG_TO_UBASE(0xD231A6B0), HN_ULONG_TO_UBASE(0x75540D94),
        HN_ULONG_TO_UBASE(0x0C4FE03D), HN_ULONG_TO_UBASE(0x524440FB),
        HN_ULONG_TO_UBASE(0xCE9738DD), HN_ULONG_TO_UBASE(0xB8665FBF),
        HN_ULONG_TO_UBASE(0x8F1BD64F), HN_ULONG_TO_UBASE(0xBB74F6A8),
        HN_ULONG_TO_UBASE(0xBB8D6C67), HN_ULONG_TO_UBASE(0x063AFA3C),
        HN_ULONG_TO_UBASE(0x00000189)
    },

    /* 2^e * 8G.y */
    {
        HN_ULONG_TO_UBASE(0xA864385F), HN_ULONG_TO_UBASE(0x259B9DA4),
        HN_ULONG_TO_UBASE(0x17C2597B), HN_ULONG_TO_UBASE(0xDBAFC55C),
        HN_ULONG_TO_UBASE(0x56BF5D23), HN_ULONG_TO_UBASE(0xDE890ACB),
        HN_ULONG_TO_UBASE(0xF8455B59), HN_ULONG_TO_UBASE(0xE51182C0),
        HN_ULONG_TO_UBASE(0xC456E1C7), HN_ULONG_TO_UBASE(0x75D51A03),
        HN_ULONG_TO_UBASE(0xB318E747), HN_ULONG_TO_UBASE(0x9C7929C6),
        HN_ULONG_TO_UBASE(0x39B3ED84), HN_ULONG_TO_UBASE(0xAF23A7F1),
        HN_ULONG_TO_UBASE(0x910F4AB2), HN_ULONG_TO_UBASE(0xEE136A2B),
        HN_ULONG_TO_UBASE(0x000001AC)
    },

    /* 2^e * 9G.x */
    {
        HN_ULONG_TO_UBASE(0x10EDB455), HN_ULONG_TO_UBASE(0x5A846285),
        HN_ULONG_TO_UBASE(0x7CB3C393), HN_ULONG_TO_UBASE(0x492D3EE0),
        HN_ULONG_TO_UBASE(0x49F1C83D), HN_ULONG_TO_UBASE(0x779FD159),
        HN_ULONG_TO_UBASE(0x0C164C56), HN_ULONG_TO_UBASE(0x70F2BD93),
        HN_ULONG_TO_UBASE(0x87270536), HN_ULONG_TO_UBASE(0xF11541B1),
        HN_ULONG_TO_UBASE(0x001F110D), HN_ULONG_TO_UBASE(0x9A1D03EC),
        HN_ULONG_TO_UBASE(0x2CD19157), HN_ULONG_TO_UBASE(0x9A9CD471),
        HN_ULONG_TO_UBASE(0x16F2F4E9), HN_ULONG_TO_UBASE(0x692625ED),
        HN_ULONG_TO_UBASE(0x00000113)
    },

    /* 2^e * 9G.y */
    {
        HN_ULONG_TO_UBASE(0xA2375E46), HN_ULONG_TO_UBASE(0xADF5832B),
        HN_ULONG_TO_UBASE(0x4E45C385), HN_ULONG_TO_UBASE(0x9B5B3203),
        HN_ULONG_TO_UBASE(0xD7314FC0), HN_ULONG_TO_UBASE(0xCEE366FB),
        HN_ULONG_TO_UBASE(0x9FB9F8DF), HN_ULONG_TO_UBASE(0x83E59B17),
        HN_ULONG_TO_UBASE(0x7616255D), HN_ULONG_TO_UBASE(0xE5B74F6A),
        HN_ULONG_TO_UBASE(0x3A87EC4B), HN_ULONG_TO_UBASE(0xBB827578),
        HN_ULONG_TO_UBASE(0x7C844501), HN_ULONG_TO_UBASE(0x4E0A3BA8),
        HN_ULONG_TO_UBASE(0x406C10EA), HN_ULONG_TO_UBASE(0x26B9131F),
        HN_ULONG_TO_UBASE(0x00000072)
    },

    /* 2^e * 10G.x */
    {
        HN_ULONG_TO_UBASE(0x1B2224AE), HN_ULONG_TO_UBASE(0x8102F95D),
        HN_ULONG_TO_UBASE(0x02003EE8), HN_ULONG_TO_UBASE(0xC8D99E6E),
        HN_ULONG_TO_UBASE(0x6769CA9A), HN_ULONG_TO_UBASE(0x62C2B5FE),
        HN_ULONG_TO_UBASE(0x21539A2F), HN_ULONG_TO_UBASE(0x33F46864),
        HN_ULONG_TO_UBASE(0x65BAE93C), HN_ULONG_TO_UBASE(0xBA60DDBC),
        HN_ULONG_TO_UBASE(0x6607E20B), HN_ULONG_TO_UBASE(0xC7DA4B45),
        HN_ULONG_TO_UBASE(0xED92F4A7), HN_ULONG_TO_UBASE(0xE2AEEE9E),
        HN_ULONG_TO_UBASE(0x0E2758C0), HN_ULONG_TO_UBASE(0x0B441AD0),
        HN_ULONG_TO_UBASE(0x00000062)
    },

    /* 2^e * 10G.y */
    {
        HN_ULONG_TO_UBASE(0xDF3933D0), HN_ULONG_TO_UBASE(0x83D72850),
        HN_ULONG_TO_UBASE(0x4A60FCCD), HN_ULONG_TO_UBASE(0x12A095B3),
        HN_ULONG_TO_UBASE(0xBD945946), HN_ULONG_TO_UBASE(0xC738C99A),
        HN_ULONG_TO_UBASE(0x1C3701C9), HN_ULONG_TO_UBASE(0xF24672E8),
        HN_ULONG_TO_UBASE(0xC32B64B2), HN_ULONG_TO_UBASE(0xDD434498),
        HN_ULONG_TO_UBASE(0xAFE1CB1B), HN_ULONG_TO_UBASE(0x186CCDD9),
        HN_ULONG_TO_UBASE(0x9231BE0D), HN_ULONG_TO_UBASE(0x08AB7CA2),
        HN_ULONG_TO_UBASE(0xD9725EB2), HN_ULONG_TO_UBASE(0x2138E749),
        HN_ULONG_TO_UBASE(0x0000008F)
    },

    /* 2^e * 11G.x */
    {
        HN_ULONG_TO_UBASE(0x8D8E4201), HN_ULONG_TO_UBASE(0x380B390D),
        HN_ULONG_TO_UBASE(0x2D998419), HN_ULONG_TO_UBASE(0xE5EA50D0),
        HN_ULONG_TO_UBASE(0xBEE95A0D), HN_ULONG_TO_UBASE(0x8A2EF170),
        HN_ULONG_TO_UBASE(0xF0E0DC21), HN_ULONG_TO_UBASE(0xF66AC193),
        HN_ULONG_TO_UBASE(0x5E7BBC57), HN_ULONG_TO_UBASE(0x95D0F8EF),
        HN_ULONG_TO_UBASE(0x2077303D), HN_ULONG_TO_UBASE(0x4BE62601),
        HN_ULONG_TO_UBASE(0xA11052F9), HN_ULONG_TO_UBASE(0x2CDDE57F),
        HN_ULONG_TO_UBASE(0x10E34694), HN_ULONG_TO_UBASE(0xB61494A5),
        HN_ULONG_TO_UBASE(0x0000005D)
    },

    /* 2^e * 11G.y */
    {
        HN_ULONG_TO_UBASE(0x33371EAD), HN_ULONG_TO_UBASE(0x66DB89E1),
        HN_ULONG_TO_UBASE(0x2FF6715D), HN_ULONG_TO_UBASE(0x4D0E181E),
        HN_ULONG_TO_UBASE(0x35DFB301), HN_ULONG_TO_UBASE(0x7E12B26D),
        HN_ULONG_TO_UBASE(0x861468AC), HN_ULONG_TO_UBASE(0x7CD336DE),
        HN_ULONG_TO_UBASE(0xB83B16B5), HN_ULONG_TO_UBASE(0x7813A4DA),
        HN_ULONG_TO_UBASE(0xCACD41AD), HN_ULONG_TO_UBASE(0x589C9008),
        HN_ULONG_TO_UBASE(0xF3E6D2BC), HN_ULONG_TO_UBASE(0x36FBC288),
        HN_ULONG_TO_UBASE(0x09BBA03F), HN_ULONG_TO_UBASE(0xCD479ED2),
        HN_ULONG_TO_UBASE(0x000001E9)
    },

    /* 2^e * 12G.x */
    {
        HN_ULONG_TO_UBASE(0x91489C53), HN_ULONG_TO_UBASE(0x25066A08),
        HN_ULONG_TO_UBASE(0x05082881), HN_ULONG_TO_UBASE(0x15A59222),
        HN_ULONG_TO_UBASE(0x2773DE00), HN_ULONG_TO_UBASE(0x5A74DBF6),
        HN_ULONG_TO_UBASE(0x5ABB1822), HN_ULONG_TO_UBASE(0x4004EA8A),
        HN_ULONG_TO_UBASE(0x44446ABA), HN_ULONG_TO_UBASE(0x7234A193),
        HN_ULONG_TO_UBASE(0xF80F21BA), HN_ULONG_TO_UBASE(0xC8039867),
        HN_ULONG_TO_UBASE(0x0B0277C1), HN_ULONG_TO_UBASE(0x57B09BF9),
        HN_ULONG_TO_UBASE(0x07FBAA90), HN_ULONG_TO_UBASE(0x90F47A5D),
        HN_ULONG_TO_UBASE(0x000001A6)
    },

    /* 2^e * 12G.y */
    {
        HN_ULONG_TO_UBASE(0x66A9943A), HN_ULONG_TO_UBASE(0x863B8C68),
        HN_ULONG_TO_UBASE(0xB3ECB8F1), HN_ULONG_TO_UBASE(0x1EB77A2A),
        HN_ULONG_TO_UBASE(0x5F0CF86A), HN_ULONG_TO_UBASE(0xC5BF5AD4),
        HN_ULONG_TO_UBASE(0x492A65D3), HN_ULONG_TO_UBASE(0x54F6D638),
        HN_ULONG_TO_UBASE(0xBB2FF375), HN_ULONG_TO_UBASE(0x74758E9C),
        HN_ULONG_TO_UBASE(0xEEF24C47), HN_ULONG_TO_UBASE(0x8E2A6AAC),
        HN_ULONG_TO_UBASE(0x5108A526), HN_ULONG_TO_UBASE(0x7D5F8E0D),
        HN_ULONG_TO_UBASE(0x0DF1CC2F), HN_ULONG_TO_UBASE(0xAC8D6017),
        HN_ULONG_TO_UBASE(0x000001E3)
    },

    /* 2^e * 13G.x */
    {
        HN_ULONG_TO_UBASE(0xCF2F551A), HN_ULONG_TO_UBASE(0x601ED1E8),
        HN_ULONG_TO_UBASE(0xAF8168FE), HN_ULONG_TO_UBASE(0x39EF8675),
        HN_ULONG_TO_UBASE(0xE3134C4E), HN_ULONG_TO_UBASE(0x9AF97216),
        HN_ULONG_TO_UBASE(0x6EE59F26), HN_ULONG_TO_UBASE(0x66D9AF36),
        HN_ULONG_TO_UBASE(0xB88A023F), HN_ULONG_TO_UBASE(0x2E6550E0),
        HN_ULONG_TO_UBASE(0x43858CE3), HN_ULONG_TO_UBASE(0x7CB91B61),
        HN_ULONG_TO_UBASE(0x57909965), HN_ULONG_TO_UBASE(0x75692C88),
        HN_ULONG_TO_UBASE(0x9075D0F2), HN_ULONG_TO_UBASE(0x04504573),
        HN_ULONG_TO_UBASE(0x000000E4)
    },

    /* 2^e * 13G.y */
    {
        HN_ULONG_TO_UBASE(0x85B9A86D), HN_ULONG_TO_UBASE(0x448ABDD4),
        HN_ULONG_TO_UBASE(0x5741BB99), HN_ULONG_TO_UBASE(0x7D30A2D6),
        HN_ULONG_TO_UBASE(0xB94CD1AA), HN_ULONG_TO_UBASE(0x11CCF882),
        HN_ULONG_TO_UBASE(0xE6FE3CB5), HN_ULONG_TO_UBASE(0x3E4DD048),
        HN_ULONG_TO_UBASE(0x35D6DA36), HN_ULONG_TO_UBASE(0x6B23A98E),
        HN_ULONG_TO_UBASE(0x10F12633), HN_ULONG_TO_UBASE(0xCCCC9D1F),
        HN_ULONG_TO_UBASE(0xE6D04EE8), HN_ULONG_TO_UBASE(0xA8A67168),
        HN_ULONG_TO_UBASE(0x9F38E9BA), HN_ULONG_TO_UBASE(0x7B0E18CB),
        HN_ULONG_TO_UBASE(0x00000174)
    },

    /* 2^e * 14G.x */
    {
        HN_ULONG_TO_UBASE(0xC47DA14D), HN_ULONG_TO_UBASE(0x76599B16),
        HN_ULONG_TO_UBASE(0xECCD8EB8), HN_ULONG_TO_UBASE(0x5CBD674E),
        HN_ULONG_TO_UBASE(0x31B1CE8C), HN_ULONG_TO_UBASE(0x0381C1BE),
        HN_ULONG_TO_UBASE(0xD9787586), HN_ULONG_TO_UBASE(0x1A4CC8A7),
        HN_ULONG_TO_UBASE(0x82D6647A), HN_ULONG_TO_UBASE(0x1E14E554),
        HN_ULONG_TO_UBASE(0xB5276105), HN_ULONG_TO_UBASE(0x0C9D21B8),
        HN_ULONG_TO_UBASE(0x555BBF90), HN_ULONG_TO_UBASE(0x01C81C60),
        HN_ULONG_TO_UBASE(0x441FD043), HN_ULONG_TO_UBASE(0xD30E5BDF),
        HN_ULONG_TO_UBASE(0x0000017F)
    },

    /* 2^e * 14G.y */
    {
        HN_ULONG_TO_UBASE(0xF2A11F1D), HN_ULONG_TO_UBASE(0x56FF1478),
        HN_ULONG_TO_UBASE(0x26BF7BFF), HN_ULONG_TO_UBASE(0x5EEFD30E),
        HN_ULONG_TO_UBASE(0xAEBEEBC0), HN_ULONG_TO_UBASE(0x91DF30F6),
        HN_ULONG_TO_UBASE(0xD467164E), HN_ULONG_TO_UBASE(0x6FA9CF72),
        HN_ULONG_TO_UBASE(0xFC9F6B8C), HN_ULONG_TO_UBASE(0xDF3EB9DE),
        HN_ULONG_TO_UBASE(0x7EB6A7AD), HN_ULONG_TO_UBASE(0x466C3ED2),
        HN_ULONG_TO_UBASE(0x39DACF99), HN_ULONG_TO_UBASE(0x9DF5BBB3),
        HN_ULONG_TO_UBASE(0x6ED70A2D), HN_ULONG_TO_UBASE(0xAAEF2093),
        HN_ULONG_TO_UBASE(0x0000009F)
    },

    /* 2^e * 15G.x */
    {
        HN_ULONG_TO_UBASE(0x2F0213E3), HN_ULONG_TO_UBASE(0x05E0CF26),
        HN_ULONG_TO_UBASE(0xC41F8B29), HN_ULONG_TO_UBASE(0x0E2C764A),
        HN_ULONG_TO_UBASE(0x2FD6407D), HN_ULONG_TO_UBASE(0x17E7DE66),
        HN_ULONG_TO_UBASE(0xE44FD06E), HN_ULONG_TO_UBASE(0xEF95AADA),
        HN_ULONG_TO_UBASE(0xE561D1BE), HN_ULONG_TO_UBASE(0x1BC6FEE3),
        HN_ULONG_TO_UBASE(0x892B2FD5), HN_ULONG_TO_UBASE(0x04BACC8C),
        HN_ULONG_TO_UBASE(0x2FC3DEF6), HN_ULONG_TO_UBASE(0xE2BE924E),
        HN_ULONG_TO_UBASE(0x137AEBFD), HN_ULONG_TO_UBASE(0xCD6EBB93),
        HN_ULONG_TO_UBASE(0x00000000)
    },

    /* 2^e * 15G.y */
    {
        HN_ULONG_TO_UBASE(0xEDF2B39B), HN_ULONG_TO_UBASE(0xE9CFE98F),
        HN_ULONG_TO_UBASE(0x4CD2BE13), HN_ULONG_TO_UBASE(0x6B57B776),
        HN_ULONG_TO_UBASE(0xDC33EC78), HN_ULONG_TO_UBASE(0x942BC3AB),
        HN_ULONG_TO_UBASE(0x45E081CE), HN_ULONG_TO_UBASE(0x99E931E8),
        HN_ULONG_TO_UBASE(0x5E55B855), HN_ULONG_TO_UBASE(0x595E70C7),
        HN_ULONG_TO_UBASE(0x163FB2D6), HN_ULONG_TO_UBASE(0x8D58B2C5),
        HN_ULONG_TO_UBASE(0x1EA48357), HN_ULONG_TO_UBASE(0x69BD6665),
        HN_ULONG_TO_UBASE(0xD1DB8EE7), HN_ULONG_TO_UBASE(0x79F54D5A),
        HN_ULONG_TO_UBASE(0x0000010F)
    },

    /* 2^e * 16G.x */
    {
        HN_ULONG_TO_UBASE(0x18E71DD8), HN_ULONG_TO_UBASE(0xCB300761),
        HN_ULONG_TO_UBASE(0x04886B27), HN_ULONG_TO_UBASE(0x01D26846),
        HN_ULONG_TO_UBASE(0x7CC1D69E), HN_ULONG_TO_UBASE(0x22871D7B),
        HN_ULONG_TO_UBASE(0xD0B3CAF9), HN_ULONG_TO_UBASE(0x1D138A47),
        HN_ULONG_TO_UBASE(0x00A7C0B0), HN_ULONG_TO_UBASE(0x53AEB974),
        HN_ULONG_TO_UBASE(0x262F9366), HN_ULONG_TO_UBASE(0x712AA1A2),
        HN_ULONG_TO_UBASE(0x6BCAE07A), HN_ULONG_TO_UBASE(0x58C84DA4),
        HN_ULONG_TO_UBASE(0xAE8708B5), HN_ULONG_TO_UBASE(0x521B7DBC),
        HN_ULONG_TO_UBASE(0x00000045)
    },

    /* 2^e * 16G.y */
    {
        HN_ULONG_TO_UBASE(0x8D3CD884), HN_ULONG_TO_UBASE(0x03BD3643),
        HN_ULONG_TO_UBASE(0x1D62A335), HN_ULONG_TO_UBASE(0x20A737EE),
        HN_ULONG_TO_UBASE(0x904F5C53), HN_ULONG_TO_UBASE(0x37E1775F),
        HN_ULONG_TO_UBASE(0x3BAA91FD), HN_ULONG_TO_UBASE(0xB76783DD),
        HN_ULONG_TO_UBASE(0x9A84E971), HN_ULONG_TO_UBASE(0x7C552118),
        HN_ULONG_TO_UBASE(0x4488EDE7), HN_ULONG_TO_UBASE(0xD085796E),
        HN_ULONG_TO_UBASE(0x5068D6DC), HN_ULONG_TO_UBASE(0xB418E708),
        HN_ULONG_TO_UBASE(0xE5275A73), HN_ULONG_TO_UBASE(0x4417E81A),
        HN_ULONG_TO_UBASE(0x000000D2)
    },

    /* 2^e * 17G.x */
    {
        HN_ULONG_TO_UBASE(0x214D97FA), HN_ULONG_TO_UBASE(0x706CD750),
        HN_ULONG_TO_UBASE(0x5F780C94), HN_ULONG_TO_UBASE(0x08FE7535),
        HN_ULONG_TO_UBASE(0xF6E319D3), HN_ULONG_TO_UBASE(0x1E0CD31C),
        HN_ULONG_TO_UBASE(0x40EE8186), HN_ULONG_TO_UBASE(0x6876C4B2),
        HN_ULONG_TO_UBASE(0xDC35B5F2), HN_ULONG_TO_UBASE(0x3576B7A1),
        HN_ULONG_TO_UBASE(0xDC7BC4FE), HN_ULONG_TO_UBASE(0xA547E6A6),
        HN_ULONG_TO_UBASE(0xD6BB9E84), HN_ULONG_TO_UBASE(0x48C9AF44),
        HN_ULONG_TO_UBASE(0x0296E1D8), HN_ULONG_TO_UBASE(0x603902C9),
        HN_ULONG_TO_UBASE(0x000000B2)
    },

    /* 2^e * 17G.y */
    {
        HN_ULONG_TO_UBASE(0xD841AC3B), HN_ULONG_TO_UBASE(0x2A879081),
        HN_ULONG_TO_UBASE(0x67931CEB), HN_ULONG_TO_UBASE(0x068D9BB6),
        HN_ULONG_TO_UBASE(0x33A82324), HN_ULONG_TO_UBASE(0x53B9F2E2),
        HN_ULONG_TO_UBASE(0xF906B735), HN_ULONG_TO_UBASE(0xEE92294F),
        HN_ULONG_TO_UBASE(0xEBF90917), HN_ULONG_TO_UBASE(0x69828276),
        HN_ULONG_TO_UBASE(0x2A62D98A), HN_ULONG_TO_UBASE(0x63F2689E),
        HN_ULONG_TO_UBASE(0xBC4ECE10), HN_ULONG_TO_UBASE(0x04B43AAF),
        HN_ULONG_TO_UBASE(0x5FA73616), HN_ULONG_TO_UBASE(0x0CFA03A9),
        HN_ULONG_TO_UBASE(0x00000195)
    },

    /* 2^e * 18G.x */
    {
        HN_ULONG_TO_UBASE(0xF3A411D3), HN_ULONG_TO_UBASE(0x73FEA98E),
        HN_ULONG_TO_UBASE(0x98E5CBCE), HN_ULONG_TO_UBASE(0x7C97905C),
        HN_ULONG_TO_UBASE(0xAF1D3F28), HN_ULONG_TO_UBASE(0x1C7610EE),
        HN_ULONG_TO_UBASE(0xD6727BA7), HN_ULONG_TO_UBASE(0xCEF371EA),
        HN_ULONG_TO_UBASE(0xAF8DD567), HN_ULONG_TO_UBASE(0xBD54FC4D),
        HN_ULONG_TO_UBASE(0xF5240B46), HN_ULONG_TO_UBASE(0x95E69AD2),
        HN_ULONG_TO_UBASE(0x999679DD), HN_ULONG_TO_UBASE(0xFAA49AB7),
        HN_ULONG_TO_UBASE(0x39FF5965), HN_ULONG_TO_UBASE(0x882E7907),
        HN_ULONG_TO_UBASE(0x000000AA)
    },

    /* 2^e * 18G.y */
    {
        HN_ULONG_TO_UBASE(0x038E2424), HN_ULONG_TO_UBASE(0x357CE2C8),
        HN_ULONG_TO_UBASE(0x694CE848), HN_ULONG_TO_UBASE(0x6E1A67BC),
        HN_ULONG_TO_UBASE(0x6F7FA9BC), HN_ULONG_TO_UBASE(0xF12A4334),
        HN_ULONG_TO_UBASE(0x83527993), HN_ULONG_TO_UBASE(0xA81D9CC6),
        HN_ULONG_TO_UBASE(0x25F39419), HN_ULONG_TO_UBASE(0xD0BBAB07),
        HN_ULONG_TO_UBASE(0xD0C3DEFD), HN_ULONG_TO_UBASE(0xAC7E3CF3),
        HN_ULONG_TO_UBASE(0x2759E25D), HN_ULONG_TO_UBASE(0x240F29B9),
        HN_ULONG_TO_UBASE(0xDA828381), HN_ULONG_TO_UBASE(0x93D17A45),
        HN_ULONG_TO_UBASE(0x0000008C)
    },

    /* 2^e * 19G.x */
    {
        HN_ULONG_TO_UBASE(0x02FCB910), HN_ULONG_TO_UBASE(0x902C8A08),
        HN_ULONG_TO_UBASE(0xF531DECF), HN_ULONG_TO_UBASE(0xCAFB63DF),
        HN_ULONG_TO_UBASE(0xF3FC2396), HN_ULONG_TO_UBASE(0xDAD73D20),
        HN_ULONG_TO_UBASE(0x0845A72D), HN_ULONG_TO_UBASE(0x5746F263),
        HN_ULONG_TO_UBASE(0xB7454756), HN_ULONG_TO_UBASE(0x8755F372),
        HN_ULONG_TO_UBASE(0x44992953), HN_ULONG_TO_UBASE(0xFFFDDC68),
        HN_ULONG_TO_UBASE(0x90DE6DBE), HN_ULONG_TO_UBASE(0xC8C2089B),
        HN_ULONG_TO_UBASE(0x12CBE3D3), HN_ULONG_TO_UBASE(0x9C892E45),
        HN_ULONG_TO_UBASE(0x000001FA)
    },

    /* 2^e * 19G.y */
    {
        HN_ULONG_TO_UBASE(0xBE470471), HN_ULONG_TO_UBASE(0xCA6AE03B),
        HN_ULONG_TO_UBASE(0xE0385EA3), HN_ULONG_TO_UBASE(0x00FFFC9D),
        HN_ULONG_TO_UBASE(0x419AAFD3), HN_ULONG_TO_UBASE(0x13F7B1C7),
        HN_ULONG_TO_UBASE(0x67B74CA1), HN_ULONG_TO_UBASE(0x8C78C5CB),
        HN_ULONG_TO_UBASE(0xFD93FD1A), HN_ULONG_TO_UBASE(0x9CBB84EA),
        HN_ULONG_TO_UBASE(0x4CA23F38), HN_ULONG_TO_UBASE(0x3A2B3037),
        HN_ULONG_TO_UBASE(0xDCA7C63D), HN_ULONG_TO_UBASE(0x6B8CEE16),
        HN_ULONG_TO_UBASE(0x77B5E211), HN_ULONG_TO_UBASE(0x676AB915),
        HN_ULONG_TO_UBASE(0x0000006A)
    },

    /* 2^e * 20G.x */
    {
        HN_ULONG_TO_UBASE(0xFF27B0B5), HN_ULONG_TO_UBASE(0x3D26B7E5),
        HN_ULONG_TO_UBASE(0x29B02F17), HN_ULONG_TO_UBASE(0x1136AA18),
        HN_ULONG_TO_UBASE(0x445A2D18), HN_ULONG_TO_UBASE(0xABA2EABB),
        HN_ULONG_TO_UBASE(0x1F49A650), HN_ULONG_TO_UBASE(0x142D8D3E),
        HN_ULONG_TO_UBASE(0xF552BAAB), HN_ULONG_TO_UBASE(0x78D632C0),
        HN_ULONG_TO_UBASE(0xA8D7A91F), HN_ULONG_TO_UBASE(0xCADCD51C),
        HN_ULONG_TO_UBASE(0x487EFFC9), HN_ULONG_TO_UBASE(0xF10758B0),
        HN_ULONG_TO_UBASE(0x80F399AC), HN_ULONG_TO_UBASE(0x7A17FD89),
        HN_ULONG_TO_UBASE(0x00000125)
    },

    /* 2^e * 20G.y */
    {
        HN_ULONG_TO_UBASE(0xF5F4FF7A), HN_ULONG_TO_UBASE(0xFC5E19FF),
        HN_ULONG_TO_UBASE(0xA7F0AE54), HN_ULONG_TO_UBASE(0x0CD69AC0),
        HN_ULONG_TO_UBASE(0x69F05E26), HN_ULONG_TO_UBASE(0xDF1ECA97),
        HN_ULONG_TO_UBASE(0xE2D705A9), HN_ULONG_TO_UBASE(0xAE28D46F),
        HN_ULONG_TO_UBASE(0x612E1D65), HN_ULONG_TO_UBASE(0x8F8E9912),
        HN_ULONG_TO_UBASE(0x34923A11), HN_ULONG_TO_UBASE(0x771F5FDF),
        HN_ULONG_TO_UBASE(0xC332DF8F), HN_ULONG_TO_UBASE(0xC06E39CE),
        HN_ULONG_TO_UBASE(0x4F10FFD1), HN_ULONG_TO_UBASE(0x6CCBE123),
        HN_ULONG_TO_UBASE(0x000000EA)
    },

    /* 2^e * 21G.x */
    {
        HN_ULONG_TO_UBASE(0x5A415FC9), HN_ULONG_TO_UBASE(0xEAFEA65F),
        HN_ULONG_TO_UBASE(0xB6524111), HN_ULONG_TO_UBASE(0xE6067847),
        HN_ULONG_TO_UBASE(0x1A1A21A6), HN_ULONG_TO_UBASE(0xB071AC62),
        HN_ULONG_TO_UBASE(0xC5EA21B1), HN_ULONG_TO_UBASE(0x7BBE62D4),
        HN_ULONG_TO_UBASE(0x302E2445), HN_ULONG_TO_UBASE(0x4BFFA5D4),
        HN_ULONG_TO_UBASE(0x65364381), HN_ULONG_TO_UBASE(0x7740EB41),
        HN_ULONG_TO_UBASE(0xA6D79B3A), HN_ULONG_TO_UBASE(0x94649FB7),
        HN_ULONG_TO_UBASE(0x67C37F98), HN_ULONG_TO_UBASE(0xC7F325B4),
        HN_ULONG_TO_UBASE(0x0000010C)
    },

    /* 2^e * 21G.y */
    {
        HN_ULONG_TO_UBASE(0x37577713), HN_ULONG_TO_UBASE(0xC872C98C),
        HN_ULONG_TO_UBASE(0xAD79810C), HN_ULONG_TO_UBASE(0xFD73F990),
        HN_ULONG_TO_UBASE(0xE12D0640), HN_ULONG_TO_UBASE(0x7E85646D),
        HN_ULONG_TO_UBASE(0xDC0CD5EE), HN_ULONG_TO_UBASE(0xEB850FEC),
        HN_ULONG_TO_UBASE(0x3109AD42), HN_ULONG_TO_UBASE(0xFD8A4FAD),
        HN_ULONG_TO_UBASE(0xC15EBE5E), HN_ULONG_TO_UBASE(0xE43B24AF),
        HN_ULONG_TO_UBASE(0xE65EBC48), HN_ULONG_TO_UBASE(0xC0F0D5CB),
        HN_ULONG_TO_UBASE(0xBCEF2AE2), HN_ULONG_TO_UBASE(0x0CB90F23),
        HN_ULONG_TO_UBASE(0x00000135)
    },

    /* 2^e * 22G.x */
    {
        HN_ULONG_TO_UBASE(0xDD9AB07E), HN_ULONG_TO_UBASE(0x51AB0502),
        HN_ULONG_TO_UBASE(0x31639329), HN_ULONG_TO_UBASE(0xFFE752C0),
        HN_ULONG_TO_UBASE(0x777D9243), HN_ULONG_TO_UBASE(0x59558384),
        HN_ULONG_TO_UBASE(0xE8CD61AC), HN_ULONG_TO_UBASE(0xCF5F9D1D),
        HN_ULONG_TO_UBASE(0x00BAD307), HN_ULONG_TO_UBASE(0xC9091087),
        HN_ULONG_TO_UBASE(0xEB9C6441), HN_ULONG_TO_UBASE(0x176ADF78),
        HN_ULONG_TO_UBASE(0xBD1ABEBC), HN_ULONG_TO_UBASE(0x6B678E90),
        HN_ULONG_TO_UBASE(0x562561AB), HN_ULONG_TO_UBASE(0xC4DD06B2),
        HN_ULONG_TO_UBASE(0x000001A7)
    },

    /* 2^e * 22G.y */
    {
        HN_ULONG_TO_UBASE(0xB826F85D), HN_ULONG_TO_UBASE(0xA9AB9A04),
        HN_ULONG_TO_UBASE(0x420A802D), HN_ULONG_TO_UBASE(0x3B12FBDC),
        HN_ULONG_TO_UBASE(0x631BFCA8), HN_ULONG_TO_UBASE(0x5F0E4787),
        HN_ULONG_TO_UBASE(0x9C4D5DFC), HN_ULONG_TO_UBASE(0xA39795EE),
        HN_ULONG_TO_UBASE(0x8778DB79), HN_ULONG_TO_UBASE(0x381180A0),
        HN_ULONG_TO_UBASE(0x31B42781), HN_ULONG_TO_UBASE(0x0602DD54),
        HN_ULONG_TO_UBASE(0x785A9AE2), HN_ULONG_TO_UBASE(0x27BEADC3),
        HN_ULONG_TO_UBASE(0xD0CDBC7B), HN_ULONG_TO_UBASE(0xEF3B5C3B),
        HN_ULONG_TO_UBASE(0x000001B2)
    },

    /* 2^e * 23G.x */
    {
        HN_ULONG_TO_UBASE(0x74589440), HN_ULONG_TO_UBASE(0xA179FDDD),
        HN_ULONG_TO_UBASE(0x234C300C), HN_ULONG_TO_UBASE(0xEC2B04AB),
        HN_ULONG_TO_UBASE(0x9DE7BE76), HN_ULONG_TO_UBASE(0xDBFEE84D),
        HN_ULONG_TO_UBASE(0x4BC3DB87), HN_ULONG_TO_UBASE(0xD1AA9274),
        HN_ULONG_TO_UBASE(0xE102DA93), HN_ULONG_TO_UBASE(0xE27F52F9),
        HN_ULONG_TO_UBASE(0x398C324F), HN_ULONG_TO_UBASE(0xC9827581),
        HN_ULONG_TO_UBASE(0x0B9CAF80), HN_ULONG_TO_UBASE(0xFC80D771),
        HN_ULONG_TO_UBASE(0x3C9F77E2), HN_ULONG_TO_UBASE(0x22AA1ADA),
        HN_ULONG_TO_UBASE(0x000000C8)
    },

    /* 2^e * 23G.y */
    {
        HN_ULONG_TO_UBASE(0xD731ABC2), HN_ULONG_TO_UBASE(0x80583409),
        HN_ULONG_TO_UBASE(0xE46D97A9), HN_ULONG_TO_UBASE(0xCF611182),
        HN_ULONG_TO_UBASE(0x4F1C3B28), HN_ULONG_TO_UBASE(0x2CD5C0EB),
        HN_ULONG_TO_UBASE(0x8F363E1A), HN_ULONG_TO_UBASE(0x4C776C78),
        HN_ULONG_TO_UBASE(0x8F3385B8), HN_ULONG_TO_UBASE(0xF4D60732),
        HN_ULONG_TO_UBASE(0xDAB5D18D), HN_ULONG_TO_UBASE(0x9A409F59),
        HN_ULONG_TO_UBASE(0x2D053736), HN_ULONG_TO_UBASE(0x51DF0AEB),
        HN_ULONG_TO_UBASE(0x5E6C9B06), HN_ULONG_TO_UBASE(0xB4A66CE7),
        HN_ULONG_TO_UBASE(0x000001B3)
    },

    /* 2^e * 24G.x */
    {
        HN_ULONG_TO_UBASE(0xA1AF2987), HN_ULONG_TO_UBASE(0x1F0491D7),
        HN_ULONG_TO_UBASE(0xF5EE72FA), HN_ULONG_TO_UBASE(0xEFB019B3),
        HN_ULONG_TO_UBASE(0x4B8FF71C), HN_ULONG_TO_UBASE(0x73232D69),
        HN_ULONG_TO_UBASE(0xAB4A9C53), HN_ULONG_TO_UBASE(0x9E9BD679),
        HN_ULONG_TO_UBASE(0x95869A29), HN_ULONG_TO_UBASE(0xF52CB78F),
        HN_ULONG_TO_UBASE(0xEBA82541), HN_ULONG_TO_UBASE(0xD9792C42),
        HN_ULONG_TO_UBASE(0x8E842BBB), HN_ULONG_TO_UBASE(0x023B2307),
        HN_ULONG_TO_UBASE(0x377703F8), HN_ULONG_TO_UBASE(0x5EF5276B),
        HN_ULONG_TO_UBASE(0x0000001C)
    },

    /* 2^e * 24G.y */
    {
        HN_ULONG_TO_UBASE(0xCA31A9CC), HN_ULONG_TO_UBASE(0x640352D4),
        HN_ULONG_TO_UBASE(0xAEA459C0), HN_ULONG_TO_UBASE(0x9FD01575),
        HN_ULONG_TO_UBASE(0x8BEAB4A2), HN_ULONG_TO_UBASE(0xC4358ECE),
        HN_ULONG_TO_UBASE(0x96AA4B07), HN_ULONG_TO_UBASE(0x3A184D17),
        HN_ULONG_TO_UBASE(0xCF60BD74), HN_ULONG_TO_UBASE(0x59DE560F),
        HN_ULONG_TO_UBASE(0xF40E4EF7), HN_ULONG_TO_UBASE(0x7A9DC454),
        HN_ULONG_TO_UBASE(0xB826C890), HN_ULONG_TO_UBASE(0xCB3035F3),
        HN_ULONG_TO_UBASE(0x9B117280), HN_ULONG_TO_UBASE(0x020FB726),
        HN_ULONG_TO_UBASE(0x0000007D)
    },

    /* 2^e * 25G.x */
    {
        HN_ULONG_TO_UBASE(0xDEFB874E), HN_ULONG_TO_UBASE(0xEF878600),
        HN_ULONG_TO_UBASE(0xC14A8051), HN_ULONG_TO_UBASE(0xE9B94A26),
        HN_ULONG_TO_UBASE(0x7D77C97F), HN_ULONG_TO_UBASE(0x0F81A278),
        HN_ULONG_TO_UBASE(0xA3E681EE), HN_ULONG_TO_UBASE(0x06965AC4),
        HN_ULONG_TO_UBASE(0x71379B8C), HN_ULONG_TO_UBASE(0x9AEED4F7),
        HN_ULONG_TO_UBASE(0xA37AC136), HN_ULONG_TO_UBASE(0xDB705990),
        HN_ULONG_TO_UBASE(0xD5823BC0), HN_ULONG_TO_UBASE(0x389A5D5D),
        HN_ULONG_TO_UBASE(0x1B4EE4E2), HN_ULONG_TO_UBASE(0x57D40581),
        HN_ULONG_TO_UBASE(0x00000135)
    },

    /* 2^e * 25G.y */
    {
        HN_ULONG_TO_UBASE(0xC7AF700E), HN_ULONG_TO_UBASE(0x636CE1BA),
        HN_ULONG_TO_UBASE(0x8B0C0D67), HN_ULONG_TO_UBASE(0xB2F9306F),
        HN_ULONG_TO_UBASE(0xE20A95C7), HN_ULONG_TO_UBASE(0xA9BD916E),
        HN_ULONG_TO_UBASE(0x497B8831), HN_ULONG_TO_UBASE(0x4837218B),
        HN_ULONG_TO_UBASE(0x86B6BC7B), HN_ULONG_TO_UBASE(0xBAF8C4D3),
        HN_ULONG_TO_UBASE(0x69E3739D), HN_ULONG_TO_UBASE(0x4F0A86B9),
        HN_ULONG_TO_UBASE(0x726613CF), HN_ULONG_TO_UBASE(0x38ED636B),
        HN_ULONG_TO_UBASE(0x75F12296), HN_ULONG_TO_UBASE(0xB8A65251),
        HN_ULONG_TO_UBASE(0x0000014B)
    },

    /* 2^e * 26G.x */
    {
        HN_ULONG_TO_UBASE(0x2BEFB3A0), HN_ULONG_TO_UBASE(0x4A18B81E),
        HN_ULONG_TO_UBASE(0xB4A83956), HN_ULONG_TO_UBASE(0x6F08D7D4),
        HN_ULONG_TO_UBASE(0xA3A6B4EB), HN_ULONG_TO_UBASE(0xA90EC94F),
        HN_ULONG_TO_UBASE(0xD108D6E7), HN_ULONG_TO_UBASE(0x747A02ED),
        HN_ULONG_TO_UBASE(0xF9119265), HN_ULONG_TO_UBASE(0x6062D725),
        HN_ULONG_TO_UBASE(0xB7A0EDF7), HN_ULONG_TO_UBASE(0x4943F52C),
        HN_ULONG_TO_UBASE(0xF06FA9D7), HN_ULONG_TO_UBASE(0x6E3EA682),
        HN_ULONG_TO_UBASE(0xBF39C61E), HN_ULONG_TO_UBASE(0x515D756E),
        HN_ULONG_TO_UBASE(0x000001A3)
    },

    /* 2^e * 26G.y */
    {
        HN_ULONG_TO_UBASE(0x40922050), HN_ULONG_TO_UBASE(0x39688D76),
        HN_ULONG_TO_UBASE(0x4BACED4B), HN_ULONG_TO_UBASE(0x8C32ABBD),
        HN_ULONG_TO_UBASE(0x8902374F), HN_ULONG_TO_UBASE(0xA3C8F88D),
        HN_ULONG_TO_UBASE(0x549E30B0), HN_ULONG_TO_UBASE(0x12BE74D4),
        HN_ULONG_TO_UBASE(0x7D821EDF), HN_ULONG_TO_UBASE(0xB2728CE5),
        HN_ULONG_TO_UBASE(0xF37EBB0E), HN_ULONG_TO_UBASE(0xB09FA111),
        HN_ULONG_TO_UBASE(0x6EA18A93), HN_ULONG_TO_UBASE(0xAC5742C9),
        HN_ULONG_TO_UBASE(0xE0781A61), HN_ULONG_TO_UBASE(0x98D97597),
        HN_ULONG_TO_UBASE(0x000001E8)
    },

    /* 2^e * 27G.x */
    {
        HN_ULONG_TO_UBASE(0xC206D597), HN_ULONG_TO_UBASE(0x43FED51D),
        HN_ULONG_TO_UBASE(0x15901BE6), HN_ULONG_TO_UBASE(0x783AD4CD),
        HN_ULONG_TO_UBASE(0x27BFFF4E), HN_ULONG_TO_UBASE(0xEFE0FDDF),
        HN_ULONG_TO_UBASE(0x7B15B710), HN_ULONG_TO_UBASE(0x5967E2B4),
        HN_ULONG_TO_UBASE(0x6BDF98CC), HN_ULONG_TO_UBASE(0x16FED041),
        HN_ULONG_TO_UBASE(0x459328C3), HN_ULONG_TO_UBASE(0xB51401B3),
        HN_ULONG_TO_UBASE(0x960F3C2A), HN_ULONG_TO_UBASE(0x852B90EC),
        HN_ULONG_TO_UBASE(0x5B7E0576), HN_ULONG_TO_UBASE(0x1125A3B5),
        HN_ULONG_TO_UBASE(0x0000013B)
    },

    /* 2^e * 27G.y */
    {
        HN_ULONG_TO_UBASE(0xE8E0082E), HN_ULONG_TO_UBASE(0x0841E7A6),
        HN_ULONG_TO_UBASE(0xD29794CB), HN_ULONG_TO_UBASE(0x35781EA4),
        HN_ULONG_TO_UBASE(0x3C7AC894), HN_ULONG_TO_UBASE(0xD6428158),
        HN_ULONG_TO_UBASE(0xA42C2608), HN_ULONG_TO_UBASE(0x6B1F1B60),
        HN_ULONG_TO_UBASE(0x9115C670), HN_ULONG_TO_UBASE(0x54140A82),
        HN_ULONG_TO_UBASE(0x4B600B19), HN_ULONG_TO_UBASE(0x13391329),
        HN_ULONG_TO_UBASE(0x3FA27D38), HN_ULONG_TO_UBASE(0x88B5AE42),
        HN_ULONG_TO_UBASE(0x91056425), HN_ULONG_TO_UBASE(0x8D7B88C0),
        HN_ULONG_TO_UBASE(0x0000010A)
    },

    /* 2^e * 28G.x */
    {
        HN_ULONG_TO_UBASE(0x8B46D3F8), HN_ULONG_TO_UBASE(0x0A2F3F39),
        HN_ULONG_TO_UBASE(0x7EA5E9F5), HN_ULONG_TO_UBASE(0x74609CC4),
        HN_ULONG_TO_UBASE(0x90BADF07), HN_ULONG_TO_UBASE(0x799C1034),
        HN_ULONG_TO_UBASE(0xE0B233EA), HN_ULONG_TO_UBASE(0x164A4076),
        HN_ULONG_TO_UBASE(0x2E774B0B), HN_ULONG_TO_UBASE(0xB520A9B3),
        HN_ULONG_TO_UBASE(0x29470BD1), HN_ULONG_TO_UBASE(0xBA5A091C),
        HN_ULONG_TO_UBASE(0xDCC3BF73), HN_ULONG_TO_UBASE(0xE2C865C4),
        HN_ULONG_TO_UBASE(0x681CFF26), HN_ULONG_TO_UBASE(0xF4282B04),
        HN_ULONG_TO_UBASE(0x000000D2)
    },

    /* 2^e * 28G.y */
    {
        HN_ULONG_TO_UBASE(0x24998F7C), HN_ULONG_TO_UBASE(0xE5969A11),
        HN_ULONG_TO_UBASE(0x363B0729), HN_ULONG_TO_UBASE(0xEDCE074B),
        HN_ULONG_TO_UBASE(0xD890DA94), HN_ULONG_TO_UBASE(0x4440C60D),
        HN_ULONG_TO_UBASE(0x03EAA158), HN_ULONG_TO_UBASE(0x77689A18),
        HN_ULONG_TO_UBASE(0x18FD5E39), HN_ULONG_TO_UBASE(0xAC028859),
        HN_ULONG_TO_UBASE(0x48312251), HN_ULONG_TO_UBASE(0xA7AB91E1),
        HN_ULONG_TO_UBASE(0x9837DE51), HN_ULONG_TO_UBASE(0x3174DDB9),
        HN_ULONG_TO_UBASE(0xB6896BAF), HN_ULONG_TO_UBASE(0x2E36BC78),
        HN_ULONG_TO_UBASE(0x0000000F)
    },

    /* 2^e * 29G.x */
    {
        HN_ULONG_TO_UBASE(0x5D17A876), HN_ULONG_TO_UBASE(0x8D44E247),
        HN_ULONG_TO_UBASE(0x18F46528), HN_ULONG_TO_UBASE(0x5B2865A2),
        HN_ULONG_TO_UBASE(0xB8C23140), HN_ULONG_TO_UBASE(0x320E0884),
        HN_ULONG_TO_UBASE(0x81FAF3B6), HN_ULONG_TO_UBASE(0xE73006F1),
        HN_ULONG_TO_UBASE(0x0D762D20), HN_ULONG_TO_UBASE(0x56DC5142),
        HN_ULONG_TO_UBASE(0xC2C03220), HN_ULONG_TO_UBASE(0x8F8A3B56),
        HN_ULONG_TO_UBASE(0x3FF0CA07), HN_ULONG_TO_UBASE(0x977E34D1),
        HN_ULONG_TO_UBASE(0xDB7AF041), HN_ULONG_TO_UBASE(0x3FD45603),
        HN_ULONG_TO_UBASE(0x000000C0)
    },

    /* 2^e * 29G.y */
    {
        HN_ULONG_TO_UBASE(0xFE79F342), HN_ULONG_TO_UBASE(0x6E5519A9),
        HN_ULONG_TO_UBASE(0x05DE7057), HN_ULONG_TO_UBASE(0xAA3980BA),
        HN_ULONG_TO_UBASE(0x9C0D4E81), HN_ULONG_TO_UBASE(0xCE830928),
        HN_ULONG_TO_UBASE(0x3CDEA8F5), HN_ULONG_TO_UBASE(0x6009A11C),
        HN_ULONG_TO_UBASE(0xBB30ACE9), HN_ULONG_TO_UBASE(0xE89D7A25),
        HN_ULONG_TO_UBASE(0x359FCD76), HN_ULONG_TO_UBASE(0x118E7ABB),
        HN_ULONG_TO_UBASE(0x4815E518), HN_ULONG_TO_UBASE(0xD0871F6D),
        HN_ULONG_TO_UBASE(0x6AA2EA4D), HN_ULONG_TO_UBASE(0xA4CE655C),
        HN_ULONG_TO_UBASE(0x000000AF)
    },

    /* 2^e * 30G.x */
    {
        HN_ULONG_TO_UBASE(0x6C500737), HN_ULONG_TO_UBASE(0x3EB3BE81),
        HN_ULONG_TO_UBASE(0x1587CBBF), HN_ULONG_TO_UBASE(0x89F48224),
        HN_ULONG_TO_UBASE(0xCD261D7F), HN_ULONG_TO_UBASE(0x3F9877E4),
        HN_ULONG_TO_UBASE(0xADFD4F51), HN_ULONG_TO_UBASE(0xF2AE780D),
        HN_ULONG_TO_UBASE(0xC8BA1966), HN_ULONG_TO_UBASE(0xE15E5259),
        HN_ULONG_TO_UBASE(0x840D4515), HN_ULONG_TO_UBASE(0xCD413B47),
        HN_ULONG_TO_UBASE(0xC7699653), HN_ULONG_TO_UBASE(0x4EE5EFA5),
        HN_ULONG_TO_UBASE(0x2B609955), HN_ULONG_TO_UBASE(0xEA69BB2C),
        HN_ULONG_TO_UBASE(0x00000162)
    },

    /* 2^e * 30G.y */
    {
        HN_ULONG_TO_UBASE(0x292A38F0), HN_ULONG_TO_UBASE(0x6DDD0387),
        HN_ULONG_TO_UBASE(0xD69B1C6A), HN_ULONG_TO_UBASE(0x8BECFF5E),
        HN_ULONG_TO_UBASE(0x058B203D), HN_ULONG_TO_UBASE(0x215F27D6),
        HN_ULONG_TO_UBASE(0x7BB1B254), HN_ULONG_TO_UBASE(0x06FB452B),
        HN_ULONG_TO_UBASE(0xEA41B3F2), HN_ULONG_TO_UBASE(0x36986BCF),
        HN_ULONG_TO_UBASE(0x82095E84), HN_ULONG_TO_UBASE(0x9265CEF9),
        HN_ULONG_TO_UBASE(0xAA24D352), HN_ULONG_TO_UBASE(0xE7F26B92),
        HN_ULONG_TO_UBASE(0x885501EF), HN_ULONG_TO_UBASE(0x8B248B76),
        HN_ULONG_TO_UBASE(0x00000023)
    },

    /* 2^e * 31G.x */
    {
        HN_ULONG_TO_UBASE(0x5E95556E), HN_ULONG_TO_UBASE(0x4DE9BE95),
        HN_ULONG_TO_UBASE(0x2AA302F0), HN_ULONG_TO_UBASE(0x8E35493E),
        HN_ULONG_TO_UBASE(0x75652B59), HN_ULONG_TO_UBASE(0x01F4511D),
        HN_ULONG_TO_UBASE(0xA6D539C0), HN_ULONG_TO_UBASE(0xDF602ACC),
        HN_ULONG_TO_UBASE(0xA4843060), HN_ULONG_TO_UBASE(0xB29FC167),
        HN_ULONG_TO_UBASE(0x0D79514B), HN_ULONG_TO_UBASE(0x908C3E41),
        HN_ULONG_TO_UBASE(0xF91DC68B), HN_ULONG_TO_UBASE(0x47482920),
        HN_ULONG_TO_UBASE(0xB0716173), HN_ULONG_TO_UBASE(0x1251AF48),
        HN_ULONG_TO_UBASE(0x00000178)
    },

    /* 2^e * 31G.y */
    {
        HN_ULONG_TO_UBASE(0x6C3214B1), HN_ULONG_TO_UBASE(0x0FF3F326),
        HN_ULONG_TO_UBASE(0x6326BF28), HN_ULONG_TO_UBASE(0x78B040CD),
        HN_ULONG_TO_UBASE(0x0E0518B4), HN_ULONG_TO_UBASE(0x8DDEEC33),
        HN_ULONG_TO_UBASE(0x6F02F5C9), HN_ULONG_TO_UBASE(0x1CD3CE74),
        HN_ULONG_TO_UBASE(0x87A46169), HN_ULONG_TO_UBASE(0x407B3F21),
        HN_ULONG_TO_UBASE(0x9FE3E626), HN_ULONG_TO_UBASE(0x48203860),
        HN_ULONG_TO_UBASE(0xCCFA97C2), HN_ULONG_TO_UBASE(0xC0DF1ECF),
        HN_ULONG_TO_UBASE(0xFB2CBDC6), HN_ULONG_TO_UBASE(0x18772C40),
        HN_ULONG_TO_UBASE(0x0000008E)
    }
};
static NX_CRYPTO_CONST NX_CRYPTO_EC_POINT secp521r1_fixed_points_array[] =
{

    /* 2G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[0],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[1],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 3G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[2],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[3],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 4G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[4],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[5],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 5G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[6],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[7],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 6G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[8],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[9],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 7G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[10],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[11],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 8G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[12],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[13],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 9G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[14],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[15],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 10G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[16],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[17],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 11G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[18],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[19],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 12G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[20],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[21],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 13G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[22],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[23],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 14G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[24],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[25],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 15G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[26],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[27],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 16G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[28],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[29],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 17G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[30],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[31],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 18G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[32],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[33],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 19G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[34],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[35],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 20G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[36],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[37],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 21G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[38],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[39],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 22G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[40],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[41],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 23G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[42],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[43],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 24G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[44],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[45],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 25G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[46],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[47],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 26G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[48],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[49],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 27G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[50],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[51],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 28G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[52],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[53],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 29G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[54],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[55],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 30G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[56],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[57],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 31G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[58],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_data[59],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    }
};
static NX_CRYPTO_CONST NX_CRYPTO_EC_POINT secp521r1_fixed_points_2e_array[] =
{

    /* 2^e * 1G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[0],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[1],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 2G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[2],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[3],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 3G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[4],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[5],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 4G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[6],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[7],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 5G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[8],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[9],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 6G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[10],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[11],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 7G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[12],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[13],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 8G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[14],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[15],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 9G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[16],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[17],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 10G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[18],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[19],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 11G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[20],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[21],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 12G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[22],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[23],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 13G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[24],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[25],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 14G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[26],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[27],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 15G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[28],
            64 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[29],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 16G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[30],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[31],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 17G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[32],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[33],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 18G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[34],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[35],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 19G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[36],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[37],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 20G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[38],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[39],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 21G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[40],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[41],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 22G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[42],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[43],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 23G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[44],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[45],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 24G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[46],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[47],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 25G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[48],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[49],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 26G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[50],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[51],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 27G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[52],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[53],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 28G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[54],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[55],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 29G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[56],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[57],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 30G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[58],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[59],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    },

    /* 2^e * 31G */
    {
        NX_CRYPTO_EC_POINT_AFFINE,
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[60],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {
            (HN_UBASE *)&secp521r1_fixed_points_2e_data[61],
            68 >> HN_SIZE_SHIFT, 68, (UINT)NX_CRYPTO_FALSE
        },
        {(HN_UBASE *)NX_CRYPTO_NULL, 0u, 0u, 0u}
    }
};


NX_CRYPTO_CONST NX_CRYPTO_EC_FIXED_POINTS _nx_crypto_ec_secp521r1_fixed_points =
{
    5u, 521u, 105u, 53u,
    (NX_CRYPTO_EC_POINT *)secp521r1_fixed_points_array,
    (NX_CRYPTO_EC_POINT *)secp521r1_fixed_points_2e_array
};

