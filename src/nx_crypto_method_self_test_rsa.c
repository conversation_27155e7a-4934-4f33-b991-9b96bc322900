/***************************************************************************
 * Copyright (c) 2024 Microsoft Corporation 
 * Copyright (c) 2025-present Eclipse ThreadX Contributors
 * 
 * This program and the accompanying materials are made available under the
 * terms of the MIT License which is available at
 * https://opensource.org/licenses/MIT.
 * 
 * SPDX-License-Identifier: MIT
 **************************************************************************/


/**************************************************************************/
/**************************************************************************/
/**                                                                       */
/** NetX Crypto Component                                                 */
/**                                                                       */
/**   Crypto Self Test                                                    */
/**                                                                       */
/**************************************************************************/
/**************************************************************************/

#define NX_CRYPTO_SOURCE_CODE


/* Include necessary system files.  */
#include "nx_crypto_method_self_test.h"


#ifdef NX_CRYPTO_SELF_TEST

/* 00010001 */
static const UCHAR pub_e_1024[] = {
0x00, 0x01, 0x00, 0x01, };

/* 54443D48BD11EC3932D60215013CA67EDCB550343E3C9460A6443E548E9DC7AC92D54DB16C1E1EE6E030C24B6BFDBFBE6815A939153D1F39B556B4332293DFC0599A022E89F83BD3CC436F8BD47270D7544454ABFB67BC60FE6C3D371B00FCD1731B39D3BE254CBC21016A4412CABB5B2AE716694B41313DDA354D89E861A7F1 */
static const UCHAR pri_e_1024[] = {
0x54, 0x44, 0x3D, 0x48, 0xBD, 0x11, 0xEC, 0x39, 0x32, 0xD6, 0x02, 0x15, 0x01, 0x3C, 0xA6, 0x7E, 
0xDC, 0xB5, 0x50, 0x34, 0x3E, 0x3C, 0x94, 0x60, 0xA6, 0x44, 0x3E, 0x54, 0x8E, 0x9D, 0xC7, 0xAC, 
0x92, 0xD5, 0x4D, 0xB1, 0x6C, 0x1E, 0x1E, 0xE6, 0xE0, 0x30, 0xC2, 0x4B, 0x6B, 0xFD, 0xBF, 0xBE, 
0x68, 0x15, 0xA9, 0x39, 0x15, 0x3D, 0x1F, 0x39, 0xB5, 0x56, 0xB4, 0x33, 0x22, 0x93, 0xDF, 0xC0, 
0x59, 0x9A, 0x02, 0x2E, 0x89, 0xF8, 0x3B, 0xD3, 0xCC, 0x43, 0x6F, 0x8B, 0xD4, 0x72, 0x70, 0xD7, 
0x54, 0x44, 0x54, 0xAB, 0xFB, 0x67, 0xBC, 0x60, 0xFE, 0x6C, 0x3D, 0x37, 0x1B, 0x00, 0xFC, 0xD1, 
0x73, 0x1B, 0x39, 0xD3, 0xBE, 0x25, 0x4C, 0xBC, 0x21, 0x01, 0x6A, 0x44, 0x12, 0xCA, 0xBB, 0x5B, 
0x2A, 0xE7, 0x16, 0x69, 0x4B, 0x41, 0x31, 0x3D, 0xDA, 0x35, 0x4D, 0x89, 0xE8, 0x61, 0xA7, 0xF1, 
};

/* AB1D8917099E72168D272D07D0CD273A6BE0B0FE31EA496DD93C2D70F9E92C7C6519F12C3A5023524FDB77FB6AE2DAFD79EAA83ECF5493564A27019DEB050C24ED82D7D1DDC6872FCEF4BDD8BBA7B119EBB6E4865328D3C9B8EB02FB6A41F3CFDE65F97BC187B25B2F2475114C70D13686B1B84096257BD75B28BD83977D35D7 */
static const UCHAR m_1024[] = {
0xAB, 0x1D, 0x89, 0x17, 0x09, 0x9E, 0x72, 0x16, 0x8D, 0x27, 0x2D, 0x07, 0xD0, 0xCD, 0x27, 0x3A, 
0x6B, 0xE0, 0xB0, 0xFE, 0x31, 0xEA, 0x49, 0x6D, 0xD9, 0x3C, 0x2D, 0x70, 0xF9, 0xE9, 0x2C, 0x7C, 
0x65, 0x19, 0xF1, 0x2C, 0x3A, 0x50, 0x23, 0x52, 0x4F, 0xDB, 0x77, 0xFB, 0x6A, 0xE2, 0xDA, 0xFD, 
0x79, 0xEA, 0xA8, 0x3E, 0xCF, 0x54, 0x93, 0x56, 0x4A, 0x27, 0x01, 0x9D, 0xEB, 0x05, 0x0C, 0x24, 
0xED, 0x82, 0xD7, 0xD1, 0xDD, 0xC6, 0x87, 0x2F, 0xCE, 0xF4, 0xBD, 0xD8, 0xBB, 0xA7, 0xB1, 0x19, 
0xEB, 0xB6, 0xE4, 0x86, 0x53, 0x28, 0xD3, 0xC9, 0xB8, 0xEB, 0x02, 0xFB, 0x6A, 0x41, 0xF3, 0xCF, 
0xDE, 0x65, 0xF9, 0x7B, 0xC1, 0x87, 0xB2, 0x5B, 0x2F, 0x24, 0x75, 0x11, 0x4C, 0x70, 0xD1, 0x36, 
0x86, 0xB1, 0xB8, 0x40, 0x96, 0x25, 0x7B, 0xD7, 0x5B, 0x28, 0xBD, 0x83, 0x97, 0x7D, 0x35, 0xD7, 
};

/* D1FBF2544869C544C27ADB0782AF4500F7BAC7479413CD180A0D67CA0E974243D40A55BE68433257637F32753899574CC107EB566F794FDAA8B5AC854A3F0783 */
static const UCHAR p_1024[] = {
0xD1, 0xFB, 0xF2, 0x54, 0x48, 0x69, 0xC5, 0x44, 0xC2, 0x7A, 0xDB, 0x07, 0x82, 0xAF, 0x45, 0x00, 
0xF7, 0xBA, 0xC7, 0x47, 0x94, 0x13, 0xCD, 0x18, 0x0A, 0x0D, 0x67, 0xCA, 0x0E, 0x97, 0x42, 0x43, 
0xD4, 0x0A, 0x55, 0xBE, 0x68, 0x43, 0x32, 0x57, 0x63, 0x7F, 0x32, 0x75, 0x38, 0x99, 0x57, 0x4C, 
0xC1, 0x07, 0xEB, 0x56, 0x6F, 0x79, 0x4F, 0xDA, 0xA8, 0xB5, 0xAC, 0x85, 0x4A, 0x3F, 0x07, 0x83, 
};

/* D09D0F7679A02A7A86047E221D2C9AEA18EF664C0E4B4C95F646BDABBB846E3B5F2DA459C5023A7C9A68242FAF6638859A8EEE975BD04A63F2061D0240A6741D */
static const UCHAR q_1024[] = {
0xD0, 0x9D, 0x0F, 0x76, 0x79, 0xA0, 0x2A, 0x7A, 0x86, 0x04, 0x7E, 0x22, 0x1D, 0x2C, 0x9A, 0xEA, 
0x18, 0xEF, 0x66, 0x4C, 0x0E, 0x4B, 0x4C, 0x95, 0xF6, 0x46, 0xBD, 0xAB, 0xBB, 0x84, 0x6E, 0x3B, 
0x5F, 0x2D, 0xA4, 0x59, 0xC5, 0x02, 0x3A, 0x7C, 0x9A, 0x68, 0x24, 0x2F, 0xAF, 0x66, 0x38, 0x85, 
0x9A, 0x8E, 0xEE, 0x97, 0x5B, 0xD0, 0x4A, 0x63, 0xF2, 0x06, 0x1D, 0x02, 0x40, 0xA6, 0x74, 0x1D, 
};

/* 6E103333C6099F3C7A70264C39D186195342BB366CEA7130D8F36328871F517803DA2C2328609425EBE03A05A801890BD0CE481AF49961241052191DA52C935C3FC2B72F8DD6F415CFFED4208FA7F55F4F36AE5FBAE1BC58E0083A0F7EE0FC202B0A0C5D1FC68C4485A96B19D6C082753A0B1F71EB804D5B749BD761A81B5224 */
static const UCHAR plain_1024[] = {
0x6E, 0x10, 0x33, 0x33, 0xC6, 0x09, 0x9F, 0x3C, 0x7A, 0x70, 0x26, 0x4C, 0x39, 0xD1, 0x86, 0x19, 
0x53, 0x42, 0xBB, 0x36, 0x6C, 0xEA, 0x71, 0x30, 0xD8, 0xF3, 0x63, 0x28, 0x87, 0x1F, 0x51, 0x78, 
0x03, 0xDA, 0x2C, 0x23, 0x28, 0x60, 0x94, 0x25, 0xEB, 0xE0, 0x3A, 0x05, 0xA8, 0x01, 0x89, 0x0B, 
0xD0, 0xCE, 0x48, 0x1A, 0xF4, 0x99, 0x61, 0x24, 0x10, 0x52, 0x19, 0x1D, 0xA5, 0x2C, 0x93, 0x5C, 
0x3F, 0xC2, 0xB7, 0x2F, 0x8D, 0xD6, 0xF4, 0x15, 0xCF, 0xFE, 0xD4, 0x20, 0x8F, 0xA7, 0xF5, 0x5F, 
0x4F, 0x36, 0xAE, 0x5F, 0xBA, 0xE1, 0xBC, 0x58, 0xE0, 0x08, 0x3A, 0x0F, 0x7E, 0xE0, 0xFC, 0x20, 
0x2B, 0x0A, 0x0C, 0x5D, 0x1F, 0xC6, 0x8C, 0x44, 0x85, 0xA9, 0x6B, 0x19, 0xD6, 0xC0, 0x82, 0x75, 
0x3A, 0x0B, 0x1F, 0x71, 0xEB, 0x80, 0x4D, 0x5B, 0x74, 0x9B, 0xD7, 0x61, 0xA8, 0x1B, 0x52, 0x24, 
};

/* 9BFFC5D6D49E777D274C4F26FE019DEE38CC1E338EA4B1D483BA922E8038F72445B5317F93FD00510C60873C5BECD3E0D6E6F1ED64B0D79043BA9F4291A5B292828AF0C15FE6F8D267FC76114CF2D7B89ABE4E2CCFD6D49A80D31153BA0DEBB2DFA1ABE75AA2F4C4D26232F270592AE70839BA2AF4D0B32B44E96F21058E0CB1 */
static const UCHAR secret_1024[] = {
0x9B, 0xFF, 0xC5, 0xD6, 0xD4, 0x9E, 0x77, 0x7D, 0x27, 0x4C, 0x4F, 0x26, 0xFE, 0x01, 0x9D, 0xEE, 
0x38, 0xCC, 0x1E, 0x33, 0x8E, 0xA4, 0xB1, 0xD4, 0x83, 0xBA, 0x92, 0x2E, 0x80, 0x38, 0xF7, 0x24, 
0x45, 0xB5, 0x31, 0x7F, 0x93, 0xFD, 0x00, 0x51, 0x0C, 0x60, 0x87, 0x3C, 0x5B, 0xEC, 0xD3, 0xE0, 
0xD6, 0xE6, 0xF1, 0xED, 0x64, 0xB0, 0xD7, 0x90, 0x43, 0xBA, 0x9F, 0x42, 0x91, 0xA5, 0xB2, 0x92, 
0x82, 0x8A, 0xF0, 0xC1, 0x5F, 0xE6, 0xF8, 0xD2, 0x67, 0xFC, 0x76, 0x11, 0x4C, 0xF2, 0xD7, 0xB8, 
0x9A, 0xBE, 0x4E, 0x2C, 0xCF, 0xD6, 0xD4, 0x9A, 0x80, 0xD3, 0x11, 0x53, 0xBA, 0x0D, 0xEB, 0xB2, 
0xDF, 0xA1, 0xAB, 0xE7, 0x5A, 0xA2, 0xF4, 0xC4, 0xD2, 0x62, 0x32, 0xF2, 0x70, 0x59, 0x2A, 0xE7, 
0x08, 0x39, 0xBA, 0x2A, 0xF4, 0xD0, 0xB3, 0x2B, 0x44, 0xE9, 0x6F, 0x21, 0x05, 0x8E, 0x0C, 0xB1, 
};


/* 00010001 */
static const UCHAR pub_e_2048[] = {
0x00, 0x01, 0x00, 0x01, };

/* 13FF7429F8E851F1079CCFCE3B3CD8606ABA8607AD85CBB3057501EBD58811F3C04823171F192C048E1E883AF8CF958810151D3874AEDC8EC4F88D2065C581569F1E200852DD40B6DFD1652659085A9DD1D3B869EA3617D904D209DE156A60BA5929D02F16430273D10720C2F28D2B95684DCAA6B9F6A508EA2CBBC11B9F3F30D6201EA6CFFBBF1C44255CEC58EE70DBC872442BCCF115D8F743557B5DE5F42DDDA6CEAE7977793CC9D90ADFE65E520F5520B615CF3B8C2DC82D7AC75EDB1297CF38AB23A37EED18D4DD45D9AD051B26401BE86E8C8E53F9585A702D02F1B5BD65F6739DFA6BFFE560CA130B6F1D4779C556C06D9CD29FB72D8851904F9CDEE9 */
static const UCHAR pri_e_2048[] = {
0x13, 0xFF, 0x74, 0x29, 0xF8, 0xE8, 0x51, 0xF1, 0x07, 0x9C, 0xCF, 0xCE, 0x3B, 0x3C, 0xD8, 0x60, 
0x6A, 0xBA, 0x86, 0x07, 0xAD, 0x85, 0xCB, 0xB3, 0x05, 0x75, 0x01, 0xEB, 0xD5, 0x88, 0x11, 0xF3, 
0xC0, 0x48, 0x23, 0x17, 0x1F, 0x19, 0x2C, 0x04, 0x8E, 0x1E, 0x88, 0x3A, 0xF8, 0xCF, 0x95, 0x88, 
0x10, 0x15, 0x1D, 0x38, 0x74, 0xAE, 0xDC, 0x8E, 0xC4, 0xF8, 0x8D, 0x20, 0x65, 0xC5, 0x81, 0x56, 
0x9F, 0x1E, 0x20, 0x08, 0x52, 0xDD, 0x40, 0xB6, 0xDF, 0xD1, 0x65, 0x26, 0x59, 0x08, 0x5A, 0x9D, 
0xD1, 0xD3, 0xB8, 0x69, 0xEA, 0x36, 0x17, 0xD9, 0x04, 0xD2, 0x09, 0xDE, 0x15, 0x6A, 0x60, 0xBA, 
0x59, 0x29, 0xD0, 0x2F, 0x16, 0x43, 0x02, 0x73, 0xD1, 0x07, 0x20, 0xC2, 0xF2, 0x8D, 0x2B, 0x95, 
0x68, 0x4D, 0xCA, 0xA6, 0xB9, 0xF6, 0xA5, 0x08, 0xEA, 0x2C, 0xBB, 0xC1, 0x1B, 0x9F, 0x3F, 0x30, 
0xD6, 0x20, 0x1E, 0xA6, 0xCF, 0xFB, 0xBF, 0x1C, 0x44, 0x25, 0x5C, 0xEC, 0x58, 0xEE, 0x70, 0xDB, 
0xC8, 0x72, 0x44, 0x2B, 0xCC, 0xF1, 0x15, 0xD8, 0xF7, 0x43, 0x55, 0x7B, 0x5D, 0xE5, 0xF4, 0x2D, 
0xDD, 0xA6, 0xCE, 0xAE, 0x79, 0x77, 0x79, 0x3C, 0xC9, 0xD9, 0x0A, 0xDF, 0xE6, 0x5E, 0x52, 0x0F, 
0x55, 0x20, 0xB6, 0x15, 0xCF, 0x3B, 0x8C, 0x2D, 0xC8, 0x2D, 0x7A, 0xC7, 0x5E, 0xDB, 0x12, 0x97, 
0xCF, 0x38, 0xAB, 0x23, 0xA3, 0x7E, 0xED, 0x18, 0xD4, 0xDD, 0x45, 0xD9, 0xAD, 0x05, 0x1B, 0x26, 
0x40, 0x1B, 0xE8, 0x6E, 0x8C, 0x8E, 0x53, 0xF9, 0x58, 0x5A, 0x70, 0x2D, 0x02, 0xF1, 0xB5, 0xBD, 
0x65, 0xF6, 0x73, 0x9D, 0xFA, 0x6B, 0xFF, 0xE5, 0x60, 0xCA, 0x13, 0x0B, 0x6F, 0x1D, 0x47, 0x79, 
0xC5, 0x56, 0xC0, 0x6D, 0x9C, 0xD2, 0x9F, 0xB7, 0x2D, 0x88, 0x51, 0x90, 0x4F, 0x9C, 0xDE, 0xE9, 
};

/* E0F5059966A8AEC4BF7CDAC8AE2430BDF61C54D09CAB9963CBF9A52AC641E384B6431D3B6A9D1811519A2904E1170A44446C80E7638A4AF2720A7654AB740D8A151FDD216F3D6933422FD9AC14AEDE9CCD021EA79E46925F4B18FD1AF2C0073CFC3A69AC71A2B3673D08136CDB01C379892601C7C857D68018DAE924CB8CD29377A14C752B92BAFF14C3A49725AE2FEFAAD4686D8A7D9F94EB11BF81E05BD5D2586526FB129E73539F9223D496B2ACA23CCACC34D5B18533BD0F5815A76F94F4F55D965FE61599B44BD8FBAD35F42B612A4C4F2765B2097A5C0090EA8166D9C6DA1E03B6119736B794600491C48433132D0F15D5DE3BB4270DF6BC9012B74931 */
static const UCHAR m_2048[] = {
0xE0, 0xF5, 0x05, 0x99, 0x66, 0xA8, 0xAE, 0xC4, 0xBF, 0x7C, 0xDA, 0xC8, 0xAE, 0x24, 0x30, 0xBD, 
0xF6, 0x1C, 0x54, 0xD0, 0x9C, 0xAB, 0x99, 0x63, 0xCB, 0xF9, 0xA5, 0x2A, 0xC6, 0x41, 0xE3, 0x84, 
0xB6, 0x43, 0x1D, 0x3B, 0x6A, 0x9D, 0x18, 0x11, 0x51, 0x9A, 0x29, 0x04, 0xE1, 0x17, 0x0A, 0x44, 
0x44, 0x6C, 0x80, 0xE7, 0x63, 0x8A, 0x4A, 0xF2, 0x72, 0x0A, 0x76, 0x54, 0xAB, 0x74, 0x0D, 0x8A, 
0x15, 0x1F, 0xDD, 0x21, 0x6F, 0x3D, 0x69, 0x33, 0x42, 0x2F, 0xD9, 0xAC, 0x14, 0xAE, 0xDE, 0x9C, 
0xCD, 0x02, 0x1E, 0xA7, 0x9E, 0x46, 0x92, 0x5F, 0x4B, 0x18, 0xFD, 0x1A, 0xF2, 0xC0, 0x07, 0x3C, 
0xFC, 0x3A, 0x69, 0xAC, 0x71, 0xA2, 0xB3, 0x67, 0x3D, 0x08, 0x13, 0x6C, 0xDB, 0x01, 0xC3, 0x79, 
0x89, 0x26, 0x01, 0xC7, 0xC8, 0x57, 0xD6, 0x80, 0x18, 0xDA, 0xE9, 0x24, 0xCB, 0x8C, 0xD2, 0x93, 
0x77, 0xA1, 0x4C, 0x75, 0x2B, 0x92, 0xBA, 0xFF, 0x14, 0xC3, 0xA4, 0x97, 0x25, 0xAE, 0x2F, 0xEF, 
0xAA, 0xD4, 0x68, 0x6D, 0x8A, 0x7D, 0x9F, 0x94, 0xEB, 0x11, 0xBF, 0x81, 0xE0, 0x5B, 0xD5, 0xD2, 
0x58, 0x65, 0x26, 0xFB, 0x12, 0x9E, 0x73, 0x53, 0x9F, 0x92, 0x23, 0xD4, 0x96, 0xB2, 0xAC, 0xA2, 
0x3C, 0xCA, 0xCC, 0x34, 0xD5, 0xB1, 0x85, 0x33, 0xBD, 0x0F, 0x58, 0x15, 0xA7, 0x6F, 0x94, 0xF4, 
0xF5, 0x5D, 0x96, 0x5F, 0xE6, 0x15, 0x99, 0xB4, 0x4B, 0xD8, 0xFB, 0xAD, 0x35, 0xF4, 0x2B, 0x61, 
0x2A, 0x4C, 0x4F, 0x27, 0x65, 0xB2, 0x09, 0x7A, 0x5C, 0x00, 0x90, 0xEA, 0x81, 0x66, 0xD9, 0xC6, 
0xDA, 0x1E, 0x03, 0xB6, 0x11, 0x97, 0x36, 0xB7, 0x94, 0x60, 0x04, 0x91, 0xC4, 0x84, 0x33, 0x13, 
0x2D, 0x0F, 0x15, 0xD5, 0xDE, 0x3B, 0xB4, 0x27, 0x0D, 0xF6, 0xBC, 0x90, 0x12, 0xB7, 0x49, 0x31, 
};

/* FBE7B456B6CC035D5DFFBA3C72D033717EA4F2FBA71CAFF70A5DFEA5AE019287850C20404E04A156AF1296A51644DEDB64292C7BDB2DCE3B5CA3C9F8AF13683E2388D0B2770C4E8302F4709BF8742A10A1DA9F45C29FE28F29F8926F8D3C3A827848BD08F156356222A7B3863609CFCF4DCFB85821A008A2E45C93A3AD12938B */
static const UCHAR p_2048[] = {
0xFB, 0xE7, 0xB4, 0x56, 0xB6, 0xCC, 0x03, 0x5D, 0x5D, 0xFF, 0xBA, 0x3C, 0x72, 0xD0, 0x33, 0x71, 
0x7E, 0xA4, 0xF2, 0xFB, 0xA7, 0x1C, 0xAF, 0xF7, 0x0A, 0x5D, 0xFE, 0xA5, 0xAE, 0x01, 0x92, 0x87, 
0x85, 0x0C, 0x20, 0x40, 0x4E, 0x04, 0xA1, 0x56, 0xAF, 0x12, 0x96, 0xA5, 0x16, 0x44, 0xDE, 0xDB, 
0x64, 0x29, 0x2C, 0x7B, 0xDB, 0x2D, 0xCE, 0x3B, 0x5C, 0xA3, 0xC9, 0xF8, 0xAF, 0x13, 0x68, 0x3E, 
0x23, 0x88, 0xD0, 0xB2, 0x77, 0x0C, 0x4E, 0x83, 0x02, 0xF4, 0x70, 0x9B, 0xF8, 0x74, 0x2A, 0x10, 
0xA1, 0xDA, 0x9F, 0x45, 0xC2, 0x9F, 0xE2, 0x8F, 0x29, 0xF8, 0x92, 0x6F, 0x8D, 0x3C, 0x3A, 0x82, 
0x78, 0x48, 0xBD, 0x08, 0xF1, 0x56, 0x35, 0x62, 0x22, 0xA7, 0xB3, 0x86, 0x36, 0x09, 0xCF, 0xCF, 
0x4D, 0xCF, 0xB8, 0x58, 0x21, 0xA0, 0x08, 0xA2, 0xE4, 0x5C, 0x93, 0xA3, 0xAD, 0x12, 0x93, 0x8B, 
};

/* E49D2C9933041944977EE1942E0AFDB69F92797C089A0649B9D85E0CD297565EDEBE29E9F43C31F18E13F3CA4B83AAE7227341C1017C4547CE649207CE824072E524E7961DFBFF5C6FBB62129BACD1457E112B3F5D140FE874EB3BC216C2A0B660C8FDB7248FA542C87BC5F31E4F363933DAD6AA0FE8E4FFEEEB5087CEF63DB3 */
static const UCHAR q_2048[] = {
0xE4, 0x9D, 0x2C, 0x99, 0x33, 0x04, 0x19, 0x44, 0x97, 0x7E, 0xE1, 0x94, 0x2E, 0x0A, 0xFD, 0xB6, 
0x9F, 0x92, 0x79, 0x7C, 0x08, 0x9A, 0x06, 0x49, 0xB9, 0xD8, 0x5E, 0x0C, 0xD2, 0x97, 0x56, 0x5E, 
0xDE, 0xBE, 0x29, 0xE9, 0xF4, 0x3C, 0x31, 0xF1, 0x8E, 0x13, 0xF3, 0xCA, 0x4B, 0x83, 0xAA, 0xE7, 
0x22, 0x73, 0x41, 0xC1, 0x01, 0x7C, 0x45, 0x47, 0xCE, 0x64, 0x92, 0x07, 0xCE, 0x82, 0x40, 0x72, 
0xE5, 0x24, 0xE7, 0x96, 0x1D, 0xFB, 0xFF, 0x5C, 0x6F, 0xBB, 0x62, 0x12, 0x9B, 0xAC, 0xD1, 0x45, 
0x7E, 0x11, 0x2B, 0x3F, 0x5D, 0x14, 0x0F, 0xE8, 0x74, 0xEB, 0x3B, 0xC2, 0x16, 0xC2, 0xA0, 0xB6, 
0x60, 0xC8, 0xFD, 0xB7, 0x24, 0x8F, 0xA5, 0x42, 0xC8, 0x7B, 0xC5, 0xF3, 0x1E, 0x4F, 0x36, 0x39, 
0x33, 0xDA, 0xD6, 0xAA, 0x0F, 0xE8, 0xE4, 0xFF, 0xEE, 0xEB, 0x50, 0x87, 0xCE, 0xF6, 0x3D, 0xB3, 
};

/* 551C2E268F7ED44D0E8B063F5B2B510CB809F53BD54E9956971E243B2363DA123C29AB4A009EDE1FCEC54625971A4E3490F3EA398BF7386AAC34720E43FB0C795445B520AEE4D7694EE1474F60F77E1B5F09FE2ED004333658D212122F040322D1564512A1540400F27E18049A762A5EDC9F072CA4F49F408252D42B31BC35523373740E90DDDA6A8CE7865EEB7C694A662C74412406AB190FE0435DA2551F0C24A48939DDA58A0239706D40B4977473689DC36CE5A4DF4EF892816CBDE2780D9389B7384674C93B1DDAF728F292B5671679FC7175AC0A3B2197B809E7CF410417010F3B1316D10D82466C62F3A01667B70A714E0499400E255D4C39EA7DE55C */
static const UCHAR plain_2048[] = {
0x55, 0x1C, 0x2E, 0x26, 0x8F, 0x7E, 0xD4, 0x4D, 0x0E, 0x8B, 0x06, 0x3F, 0x5B, 0x2B, 0x51, 0x0C, 
0xB8, 0x09, 0xF5, 0x3B, 0xD5, 0x4E, 0x99, 0x56, 0x97, 0x1E, 0x24, 0x3B, 0x23, 0x63, 0xDA, 0x12, 
0x3C, 0x29, 0xAB, 0x4A, 0x00, 0x9E, 0xDE, 0x1F, 0xCE, 0xC5, 0x46, 0x25, 0x97, 0x1A, 0x4E, 0x34, 
0x90, 0xF3, 0xEA, 0x39, 0x8B, 0xF7, 0x38, 0x6A, 0xAC, 0x34, 0x72, 0x0E, 0x43, 0xFB, 0x0C, 0x79, 
0x54, 0x45, 0xB5, 0x20, 0xAE, 0xE4, 0xD7, 0x69, 0x4E, 0xE1, 0x47, 0x4F, 0x60, 0xF7, 0x7E, 0x1B, 
0x5F, 0x09, 0xFE, 0x2E, 0xD0, 0x04, 0x33, 0x36, 0x58, 0xD2, 0x12, 0x12, 0x2F, 0x04, 0x03, 0x22, 
0xD1, 0x56, 0x45, 0x12, 0xA1, 0x54, 0x04, 0x00, 0xF2, 0x7E, 0x18, 0x04, 0x9A, 0x76, 0x2A, 0x5E, 
0xDC, 0x9F, 0x07, 0x2C, 0xA4, 0xF4, 0x9F, 0x40, 0x82, 0x52, 0xD4, 0x2B, 0x31, 0xBC, 0x35, 0x52, 
0x33, 0x73, 0x74, 0x0E, 0x90, 0xDD, 0xDA, 0x6A, 0x8C, 0xE7, 0x86, 0x5E, 0xEB, 0x7C, 0x69, 0x4A, 
0x66, 0x2C, 0x74, 0x41, 0x24, 0x06, 0xAB, 0x19, 0x0F, 0xE0, 0x43, 0x5D, 0xA2, 0x55, 0x1F, 0x0C, 
0x24, 0xA4, 0x89, 0x39, 0xDD, 0xA5, 0x8A, 0x02, 0x39, 0x70, 0x6D, 0x40, 0xB4, 0x97, 0x74, 0x73, 
0x68, 0x9D, 0xC3, 0x6C, 0xE5, 0xA4, 0xDF, 0x4E, 0xF8, 0x92, 0x81, 0x6C, 0xBD, 0xE2, 0x78, 0x0D, 
0x93, 0x89, 0xB7, 0x38, 0x46, 0x74, 0xC9, 0x3B, 0x1D, 0xDA, 0xF7, 0x28, 0xF2, 0x92, 0xB5, 0x67, 
0x16, 0x79, 0xFC, 0x71, 0x75, 0xAC, 0x0A, 0x3B, 0x21, 0x97, 0xB8, 0x09, 0xE7, 0xCF, 0x41, 0x04, 
0x17, 0x01, 0x0F, 0x3B, 0x13, 0x16, 0xD1, 0x0D, 0x82, 0x46, 0x6C, 0x62, 0xF3, 0xA0, 0x16, 0x67, 
0xB7, 0x0A, 0x71, 0x4E, 0x04, 0x99, 0x40, 0x0E, 0x25, 0x5D, 0x4C, 0x39, 0xEA, 0x7D, 0xE5, 0x5C, 
};

/* 10F904E071338569EC131401A7869F42F3BCAE252B5D3C8755FD24D47997A9CD4221D992B2871E05283B98841FC5C379C5D0E35B3938279B344299C3CF1566E0C994D0A9013AF64174F1379A4B5E4E9DE57491F3078F6D10011EA55535D0763E538662C9996F4FCF8B64A768685AA417ADB6978743D3D1F513CF143DD6D383AD6357728A88928D39E27EA4D0B2AF92FC7F63875F9D6A70FAE7993C1FF04DF9A2F99216874BC123D4B7DA7E7E8974CFC10ACF0C7BC8747526A8D16791F969082EA9B0C36D77B67C37B325682D74178E4234D52D5635273301A6CC35E315AE74D659B1433576DAAE6780FA39E0550D971F2CB5817CAAFC24B5220E21C8CEEE85DD */
static const UCHAR secret_2048[] = {
0x10, 0xF9, 0x04, 0xE0, 0x71, 0x33, 0x85, 0x69, 0xEC, 0x13, 0x14, 0x01, 0xA7, 0x86, 0x9F, 0x42, 
0xF3, 0xBC, 0xAE, 0x25, 0x2B, 0x5D, 0x3C, 0x87, 0x55, 0xFD, 0x24, 0xD4, 0x79, 0x97, 0xA9, 0xCD, 
0x42, 0x21, 0xD9, 0x92, 0xB2, 0x87, 0x1E, 0x05, 0x28, 0x3B, 0x98, 0x84, 0x1F, 0xC5, 0xC3, 0x79, 
0xC5, 0xD0, 0xE3, 0x5B, 0x39, 0x38, 0x27, 0x9B, 0x34, 0x42, 0x99, 0xC3, 0xCF, 0x15, 0x66, 0xE0, 
0xC9, 0x94, 0xD0, 0xA9, 0x01, 0x3A, 0xF6, 0x41, 0x74, 0xF1, 0x37, 0x9A, 0x4B, 0x5E, 0x4E, 0x9D, 
0xE5, 0x74, 0x91, 0xF3, 0x07, 0x8F, 0x6D, 0x10, 0x01, 0x1E, 0xA5, 0x55, 0x35, 0xD0, 0x76, 0x3E, 
0x53, 0x86, 0x62, 0xC9, 0x99, 0x6F, 0x4F, 0xCF, 0x8B, 0x64, 0xA7, 0x68, 0x68, 0x5A, 0xA4, 0x17, 
0xAD, 0xB6, 0x97, 0x87, 0x43, 0xD3, 0xD1, 0xF5, 0x13, 0xCF, 0x14, 0x3D, 0xD6, 0xD3, 0x83, 0xAD, 
0x63, 0x57, 0x72, 0x8A, 0x88, 0x92, 0x8D, 0x39, 0xE2, 0x7E, 0xA4, 0xD0, 0xB2, 0xAF, 0x92, 0xFC, 
0x7F, 0x63, 0x87, 0x5F, 0x9D, 0x6A, 0x70, 0xFA, 0xE7, 0x99, 0x3C, 0x1F, 0xF0, 0x4D, 0xF9, 0xA2, 
0xF9, 0x92, 0x16, 0x87, 0x4B, 0xC1, 0x23, 0xD4, 0xB7, 0xDA, 0x7E, 0x7E, 0x89, 0x74, 0xCF, 0xC1, 
0x0A, 0xCF, 0x0C, 0x7B, 0xC8, 0x74, 0x75, 0x26, 0xA8, 0xD1, 0x67, 0x91, 0xF9, 0x69, 0x08, 0x2E, 
0xA9, 0xB0, 0xC3, 0x6D, 0x77, 0xB6, 0x7C, 0x37, 0xB3, 0x25, 0x68, 0x2D, 0x74, 0x17, 0x8E, 0x42, 
0x34, 0xD5, 0x2D, 0x56, 0x35, 0x27, 0x33, 0x01, 0xA6, 0xCC, 0x35, 0xE3, 0x15, 0xAE, 0x74, 0xD6, 
0x59, 0xB1, 0x43, 0x35, 0x76, 0xDA, 0xAE, 0x67, 0x80, 0xFA, 0x39, 0xE0, 0x55, 0x0D, 0x97, 0x1F, 
0x2C, 0xB5, 0x81, 0x7C, 0xAA, 0xFC, 0x24, 0xB5, 0x22, 0x0E, 0x21, 0xC8, 0xCE, 0xEE, 0x85, 0xDD, 
};

/* 00010001 */
static UCHAR pub_e_3072[] = {
0x00, 0x01, 0x00, 0x01, };

/* 5956069136AA94BD53D6A5741A617BDF380FADB5F5116D264A6CB4FCEBE62EB75C7664D53F2AAF5A4F8BDF0828CC7DC86E1708955231D04FD87942EFD9D7BC168661CF994A64229BB421B3D5F24E5939EB5D58BDD6D9A26C4BF6F7D20F627B5C13D8D48EC250C8A0578570FCF16A39C51BE1D0AE87D812D954DBD6B3B81A6019E12D902CC2B93B9CF048BC1A4C48FF404D6F5FDF0215AEB865CBC8E80D871005168ECDB5BCAB76FB5CC0B9431D1FD9A6105380F4084F6C00D437F38889E13E654C1256D43E613C9D9FCF96D98F94AC861EC16CEB9B2F6B7B43E593CD6432489F7619E52D87E2938D9502F267080407526C2E5872273D6BB74E808BE4B08AC24CA1C2E72EAC695861E758D666E4A9A4DC3B2DB37E2AB06C1C0AB320716BA19EB353C79EABBE411F976A3CB4C993A930F357E8761155F6BDDFBA3D0F4AEC306BFAF4DB13245ECD4B9082BAAE950121EA5E9D018027C17E82FF5D038717C1FA2D0311FFAC624F715FFD222103CE157162B7DC7AE0C4D1BF9605F8620A5063323191 */
static UCHAR pri_e_3072[] = {
0x59, 0x56, 0x06, 0x91, 0x36, 0xAA, 0x94, 0xBD, 0x53, 0xD6, 0xA5, 0x74, 0x1A, 0x61, 0x7B, 0xDF, 
0x38, 0x0F, 0xAD, 0xB5, 0xF5, 0x11, 0x6D, 0x26, 0x4A, 0x6C, 0xB4, 0xFC, 0xEB, 0xE6, 0x2E, 0xB7, 
0x5C, 0x76, 0x64, 0xD5, 0x3F, 0x2A, 0xAF, 0x5A, 0x4F, 0x8B, 0xDF, 0x08, 0x28, 0xCC, 0x7D, 0xC8, 
0x6E, 0x17, 0x08, 0x95, 0x52, 0x31, 0xD0, 0x4F, 0xD8, 0x79, 0x42, 0xEF, 0xD9, 0xD7, 0xBC, 0x16, 
0x86, 0x61, 0xCF, 0x99, 0x4A, 0x64, 0x22, 0x9B, 0xB4, 0x21, 0xB3, 0xD5, 0xF2, 0x4E, 0x59, 0x39, 
0xEB, 0x5D, 0x58, 0xBD, 0xD6, 0xD9, 0xA2, 0x6C, 0x4B, 0xF6, 0xF7, 0xD2, 0x0F, 0x62, 0x7B, 0x5C, 
0x13, 0xD8, 0xD4, 0x8E, 0xC2, 0x50, 0xC8, 0xA0, 0x57, 0x85, 0x70, 0xFC, 0xF1, 0x6A, 0x39, 0xC5, 
0x1B, 0xE1, 0xD0, 0xAE, 0x87, 0xD8, 0x12, 0xD9, 0x54, 0xDB, 0xD6, 0xB3, 0xB8, 0x1A, 0x60, 0x19, 
0xE1, 0x2D, 0x90, 0x2C, 0xC2, 0xB9, 0x3B, 0x9C, 0xF0, 0x48, 0xBC, 0x1A, 0x4C, 0x48, 0xFF, 0x40, 
0x4D, 0x6F, 0x5F, 0xDF, 0x02, 0x15, 0xAE, 0xB8, 0x65, 0xCB, 0xC8, 0xE8, 0x0D, 0x87, 0x10, 0x05, 
0x16, 0x8E, 0xCD, 0xB5, 0xBC, 0xAB, 0x76, 0xFB, 0x5C, 0xC0, 0xB9, 0x43, 0x1D, 0x1F, 0xD9, 0xA6, 
0x10, 0x53, 0x80, 0xF4, 0x08, 0x4F, 0x6C, 0x00, 0xD4, 0x37, 0xF3, 0x88, 0x89, 0xE1, 0x3E, 0x65, 
0x4C, 0x12, 0x56, 0xD4, 0x3E, 0x61, 0x3C, 0x9D, 0x9F, 0xCF, 0x96, 0xD9, 0x8F, 0x94, 0xAC, 0x86, 
0x1E, 0xC1, 0x6C, 0xEB, 0x9B, 0x2F, 0x6B, 0x7B, 0x43, 0xE5, 0x93, 0xCD, 0x64, 0x32, 0x48, 0x9F, 
0x76, 0x19, 0xE5, 0x2D, 0x87, 0xE2, 0x93, 0x8D, 0x95, 0x02, 0xF2, 0x67, 0x08, 0x04, 0x07, 0x52, 
0x6C, 0x2E, 0x58, 0x72, 0x27, 0x3D, 0x6B, 0xB7, 0x4E, 0x80, 0x8B, 0xE4, 0xB0, 0x8A, 0xC2, 0x4C, 
0xA1, 0xC2, 0xE7, 0x2E, 0xAC, 0x69, 0x58, 0x61, 0xE7, 0x58, 0xD6, 0x66, 0xE4, 0xA9, 0xA4, 0xDC, 
0x3B, 0x2D, 0xB3, 0x7E, 0x2A, 0xB0, 0x6C, 0x1C, 0x0A, 0xB3, 0x20, 0x71, 0x6B, 0xA1, 0x9E, 0xB3, 
0x53, 0xC7, 0x9E, 0xAB, 0xBE, 0x41, 0x1F, 0x97, 0x6A, 0x3C, 0xB4, 0xC9, 0x93, 0xA9, 0x30, 0xF3, 
0x57, 0xE8, 0x76, 0x11, 0x55, 0xF6, 0xBD, 0xDF, 0xBA, 0x3D, 0x0F, 0x4A, 0xEC, 0x30, 0x6B, 0xFA, 
0xF4, 0xDB, 0x13, 0x24, 0x5E, 0xCD, 0x4B, 0x90, 0x82, 0xBA, 0xAE, 0x95, 0x01, 0x21, 0xEA, 0x5E, 
0x9D, 0x01, 0x80, 0x27, 0xC1, 0x7E, 0x82, 0xFF, 0x5D, 0x03, 0x87, 0x17, 0xC1, 0xFA, 0x2D, 0x03, 
0x11, 0xFF, 0xAC, 0x62, 0x4F, 0x71, 0x5F, 0xFD, 0x22, 0x21, 0x03, 0xCE, 0x15, 0x71, 0x62, 0xB7, 
0xDC, 0x7A, 0xE0, 0xC4, 0xD1, 0xBF, 0x96, 0x05, 0xF8, 0x62, 0x0A, 0x50, 0x63, 0x32, 0x31, 0x91, 
};

/* DFBF81E7BAD97A2ED41F1FA3048B923CF30BBF5E2C09D819D2E39CE431D8D568A4E2B62E6E7DF99C883480BFAC4CE20AEFBEA26D126EDE962E7B4897E24132014BC66A4BF52288FC018035C40295EF3BE8B56239EECBB87BB93D68636405273F10B581073303F8B16E801B92B96CC0CD954AEA9673F52FBA1AEA626570F8075206AAB164EC7670AA94B4044B91F34BF5E90B911EA19183DEBDE55B6C69C4BF70FE3F6B0664687B7E3B44A65094440A6D9CACF1408385841644DCD3992BEF4AF66B8111818E43EC4841D98815614CF6EC91C8A1422E6FA812C1D346BF96D5F1F5BB3EC7E6974365F67CCB70DD38C59C0D0B7E50CAA97A00D00A3443D9EDFFE889588402661983AB7B1E6E6E38A2A4096EEB1714DEEB7511366582C50202951FCED7F9D35074656103D611088694352BB4531ACFDE7C1C66B3888BE20268904D51C1AF58A09BA4378B9522BB7697D36E106A1DC323D748576BE3F9834A67BB18AD8C3F04C41023094C868D85F4DECE939A61FEE45D513FE1994D08A42D0FCF86DF */
static UCHAR m_3072[] = {
0xDF, 0xBF, 0x81, 0xE7, 0xBA, 0xD9, 0x7A, 0x2E, 0xD4, 0x1F, 0x1F, 0xA3, 0x04, 0x8B, 0x92, 0x3C, 
0xF3, 0x0B, 0xBF, 0x5E, 0x2C, 0x09, 0xD8, 0x19, 0xD2, 0xE3, 0x9C, 0xE4, 0x31, 0xD8, 0xD5, 0x68, 
0xA4, 0xE2, 0xB6, 0x2E, 0x6E, 0x7D, 0xF9, 0x9C, 0x88, 0x34, 0x80, 0xBF, 0xAC, 0x4C, 0xE2, 0x0A, 
0xEF, 0xBE, 0xA2, 0x6D, 0x12, 0x6E, 0xDE, 0x96, 0x2E, 0x7B, 0x48, 0x97, 0xE2, 0x41, 0x32, 0x01, 
0x4B, 0xC6, 0x6A, 0x4B, 0xF5, 0x22, 0x88, 0xFC, 0x01, 0x80, 0x35, 0xC4, 0x02, 0x95, 0xEF, 0x3B, 
0xE8, 0xB5, 0x62, 0x39, 0xEE, 0xCB, 0xB8, 0x7B, 0xB9, 0x3D, 0x68, 0x63, 0x64, 0x05, 0x27, 0x3F, 
0x10, 0xB5, 0x81, 0x07, 0x33, 0x03, 0xF8, 0xB1, 0x6E, 0x80, 0x1B, 0x92, 0xB9, 0x6C, 0xC0, 0xCD, 
0x95, 0x4A, 0xEA, 0x96, 0x73, 0xF5, 0x2F, 0xBA, 0x1A, 0xEA, 0x62, 0x65, 0x70, 0xF8, 0x07, 0x52, 
0x06, 0xAA, 0xB1, 0x64, 0xEC, 0x76, 0x70, 0xAA, 0x94, 0xB4, 0x04, 0x4B, 0x91, 0xF3, 0x4B, 0xF5, 
0xE9, 0x0B, 0x91, 0x1E, 0xA1, 0x91, 0x83, 0xDE, 0xBD, 0xE5, 0x5B, 0x6C, 0x69, 0xC4, 0xBF, 0x70, 
0xFE, 0x3F, 0x6B, 0x06, 0x64, 0x68, 0x7B, 0x7E, 0x3B, 0x44, 0xA6, 0x50, 0x94, 0x44, 0x0A, 0x6D, 
0x9C, 0xAC, 0xF1, 0x40, 0x83, 0x85, 0x84, 0x16, 0x44, 0xDC, 0xD3, 0x99, 0x2B, 0xEF, 0x4A, 0xF6, 
0x6B, 0x81, 0x11, 0x81, 0x8E, 0x43, 0xEC, 0x48, 0x41, 0xD9, 0x88, 0x15, 0x61, 0x4C, 0xF6, 0xEC, 
0x91, 0xC8, 0xA1, 0x42, 0x2E, 0x6F, 0xA8, 0x12, 0xC1, 0xD3, 0x46, 0xBF, 0x96, 0xD5, 0xF1, 0xF5, 
0xBB, 0x3E, 0xC7, 0xE6, 0x97, 0x43, 0x65, 0xF6, 0x7C, 0xCB, 0x70, 0xDD, 0x38, 0xC5, 0x9C, 0x0D, 
0x0B, 0x7E, 0x50, 0xCA, 0xA9, 0x7A, 0x00, 0xD0, 0x0A, 0x34, 0x43, 0xD9, 0xED, 0xFF, 0xE8, 0x89, 
0x58, 0x84, 0x02, 0x66, 0x19, 0x83, 0xAB, 0x7B, 0x1E, 0x6E, 0x6E, 0x38, 0xA2, 0xA4, 0x09, 0x6E, 
0xEB, 0x17, 0x14, 0xDE, 0xEB, 0x75, 0x11, 0x36, 0x65, 0x82, 0xC5, 0x02, 0x02, 0x95, 0x1F, 0xCE, 
0xD7, 0xF9, 0xD3, 0x50, 0x74, 0x65, 0x61, 0x03, 0xD6, 0x11, 0x08, 0x86, 0x94, 0x35, 0x2B, 0xB4, 
0x53, 0x1A, 0xCF, 0xDE, 0x7C, 0x1C, 0x66, 0xB3, 0x88, 0x8B, 0xE2, 0x02, 0x68, 0x90, 0x4D, 0x51, 
0xC1, 0xAF, 0x58, 0xA0, 0x9B, 0xA4, 0x37, 0x8B, 0x95, 0x22, 0xBB, 0x76, 0x97, 0xD3, 0x6E, 0x10, 
0x6A, 0x1D, 0xC3, 0x23, 0xD7, 0x48, 0x57, 0x6B, 0xE3, 0xF9, 0x83, 0x4A, 0x67, 0xBB, 0x18, 0xAD, 
0x8C, 0x3F, 0x04, 0xC4, 0x10, 0x23, 0x09, 0x4C, 0x86, 0x8D, 0x85, 0xF4, 0xDE, 0xCE, 0x93, 0x9A, 
0x61, 0xFE, 0xE4, 0x5D, 0x51, 0x3F, 0xE1, 0x99, 0x4D, 0x08, 0xA4, 0x2D, 0x0F, 0xCF, 0x86, 0xDF, 
};

/* FA38C2A2D9627E1FF89BA1BF0D4C57370A9C0CA97597802CB1792BC260EFE1AD4860FC1D2CA0916811AF15C8566E5EED10D4843A15978C879A7AC11A10F504BB6260508E5236ACD225BB4C8E8B353BD82EA6A158D9236DDEA5B6F4F1307DA12DE5ADA985BDE88D65C8300C2901DF6ED61C7B801568F58C0BBFE156D9F9BDC36990C980662321E9A2A7290511F71D4BF08EE03DB1B642E2944797642C243EA1AF7384A5F9AC03D475C54EBD98C620E86A9AEB51003F05BA6FBC55D2A447B091C7 */
static UCHAR p_3072[] = {
0xFA, 0x38, 0xC2, 0xA2, 0xD9, 0x62, 0x7E, 0x1F, 0xF8, 0x9B, 0xA1, 0xBF, 0x0D, 0x4C, 0x57, 0x37, 
0x0A, 0x9C, 0x0C, 0xA9, 0x75, 0x97, 0x80, 0x2C, 0xB1, 0x79, 0x2B, 0xC2, 0x60, 0xEF, 0xE1, 0xAD, 
0x48, 0x60, 0xFC, 0x1D, 0x2C, 0xA0, 0x91, 0x68, 0x11, 0xAF, 0x15, 0xC8, 0x56, 0x6E, 0x5E, 0xED, 
0x10, 0xD4, 0x84, 0x3A, 0x15, 0x97, 0x8C, 0x87, 0x9A, 0x7A, 0xC1, 0x1A, 0x10, 0xF5, 0x04, 0xBB, 
0x62, 0x60, 0x50, 0x8E, 0x52, 0x36, 0xAC, 0xD2, 0x25, 0xBB, 0x4C, 0x8E, 0x8B, 0x35, 0x3B, 0xD8, 
0x2E, 0xA6, 0xA1, 0x58, 0xD9, 0x23, 0x6D, 0xDE, 0xA5, 0xB6, 0xF4, 0xF1, 0x30, 0x7D, 0xA1, 0x2D, 
0xE5, 0xAD, 0xA9, 0x85, 0xBD, 0xE8, 0x8D, 0x65, 0xC8, 0x30, 0x0C, 0x29, 0x01, 0xDF, 0x6E, 0xD6, 
0x1C, 0x7B, 0x80, 0x15, 0x68, 0xF5, 0x8C, 0x0B, 0xBF, 0xE1, 0x56, 0xD9, 0xF9, 0xBD, 0xC3, 0x69, 
0x90, 0xC9, 0x80, 0x66, 0x23, 0x21, 0xE9, 0xA2, 0xA7, 0x29, 0x05, 0x11, 0xF7, 0x1D, 0x4B, 0xF0, 
0x8E, 0xE0, 0x3D, 0xB1, 0xB6, 0x42, 0xE2, 0x94, 0x47, 0x97, 0x64, 0x2C, 0x24, 0x3E, 0xA1, 0xAF, 
0x73, 0x84, 0xA5, 0xF9, 0xAC, 0x03, 0xD4, 0x75, 0xC5, 0x4E, 0xBD, 0x98, 0xC6, 0x20, 0xE8, 0x6A, 
0x9A, 0xEB, 0x51, 0x00, 0x3F, 0x05, 0xBA, 0x6F, 0xBC, 0x55, 0xD2, 0xA4, 0x47, 0xB0, 0x91, 0xC7, 
};

/* E4EA3E1379C8DFCD3D07B69A5B6A77F12291257532A2F79C7FDA9CB9D7B7C7CF8A16A768831F659F2BF44C8130EDF78D25702DD35A1442F2C807C0FA942204E84EAED7E4F369DE0A3FE7BD24BC0AA7EC9D181C74190B4746F901DFBBB481B2F5CD6034713C419494072A935E43089C2339EBD3C3AC92AD78B2169FDC0F43F1FA6B3EC6661AD8C90667626E16713BDB44BC5F72BF55226903F2E0EE8D119A0A35A067D09E9F4B62357C4CFF9AEC23E100923AC3E1C95DE9C9439B1B50DD766229 */
static UCHAR q_3072[] = {
0xE4, 0xEA, 0x3E, 0x13, 0x79, 0xC8, 0xDF, 0xCD, 0x3D, 0x07, 0xB6, 0x9A, 0x5B, 0x6A, 0x77, 0xF1, 
0x22, 0x91, 0x25, 0x75, 0x32, 0xA2, 0xF7, 0x9C, 0x7F, 0xDA, 0x9C, 0xB9, 0xD7, 0xB7, 0xC7, 0xCF, 
0x8A, 0x16, 0xA7, 0x68, 0x83, 0x1F, 0x65, 0x9F, 0x2B, 0xF4, 0x4C, 0x81, 0x30, 0xED, 0xF7, 0x8D, 
0x25, 0x70, 0x2D, 0xD3, 0x5A, 0x14, 0x42, 0xF2, 0xC8, 0x07, 0xC0, 0xFA, 0x94, 0x22, 0x04, 0xE8, 
0x4E, 0xAE, 0xD7, 0xE4, 0xF3, 0x69, 0xDE, 0x0A, 0x3F, 0xE7, 0xBD, 0x24, 0xBC, 0x0A, 0xA7, 0xEC, 
0x9D, 0x18, 0x1C, 0x74, 0x19, 0x0B, 0x47, 0x46, 0xF9, 0x01, 0xDF, 0xBB, 0xB4, 0x81, 0xB2, 0xF5, 
0xCD, 0x60, 0x34, 0x71, 0x3C, 0x41, 0x94, 0x94, 0x07, 0x2A, 0x93, 0x5E, 0x43, 0x08, 0x9C, 0x23, 
0x39, 0xEB, 0xD3, 0xC3, 0xAC, 0x92, 0xAD, 0x78, 0xB2, 0x16, 0x9F, 0xDC, 0x0F, 0x43, 0xF1, 0xFA, 
0x6B, 0x3E, 0xC6, 0x66, 0x1A, 0xD8, 0xC9, 0x06, 0x67, 0x62, 0x6E, 0x16, 0x71, 0x3B, 0xDB, 0x44, 
0xBC, 0x5F, 0x72, 0xBF, 0x55, 0x22, 0x69, 0x03, 0xF2, 0xE0, 0xEE, 0x8D, 0x11, 0x9A, 0x0A, 0x35, 
0xA0, 0x67, 0xD0, 0x9E, 0x9F, 0x4B, 0x62, 0x35, 0x7C, 0x4C, 0xFF, 0x9A, 0xEC, 0x23, 0xE1, 0x00, 
0x92, 0x3A, 0xC3, 0xE1, 0xC9, 0x5D, 0xE9, 0xC9, 0x43, 0x9B, 0x1B, 0x50, 0xDD, 0x76, 0x62, 0x29, 
};

/* 3748C03935ED0019AE9D855DFFBF800C14CC465DB3F68A7C8D50797D7368F74ABC2E8E0DB98D2E62AD4EA45C5536A2503920B20102100B2FCDB88A69E2BEB758D2E3AE0EE8CF51429A50131CBBB7587A7B49D016B22F307627A74B6B9924EB448148F31BBE546B058094BC01ACF674501831F50DE1E6BD418381A1624F79B54716D4BE5A321F27404F3936542AA00538E515B23CDC89AF519D08FD02A144404A9517DE334B57A15FF67AE21ACE3790354D67AC0EC3336D04B1F6470E1F4B5B1DAC03BF464B475B2ADA02B417274D8F5DFD768B2002AAFF02C0717A227EBF7E3CC0FE6A08400637242AB6F30CD82F601621EDF465AE37956F27A9155E37C1B340E056BC2F76E24B326261B978C56C6E6C536CFB03FF69B67B66B1AE36E883D9374AC1575B5C2C9151B7BB696D9728046A2060FE5568B2B17BB7735F07CC63BD1CB3F90C269176131FF3B04C7AB070984693201322B322C71C2F301703531F7E2AF428FE4059E60A102B4FDE401516F326071EA07F53F8F31E4DD7A667E7745C2F */
static UCHAR plain_3072[] = {
0x37, 0x48, 0xC0, 0x39, 0x35, 0xED, 0x00, 0x19, 0xAE, 0x9D, 0x85, 0x5D, 0xFF, 0xBF, 0x80, 0x0C, 
0x14, 0xCC, 0x46, 0x5D, 0xB3, 0xF6, 0x8A, 0x7C, 0x8D, 0x50, 0x79, 0x7D, 0x73, 0x68, 0xF7, 0x4A, 
0xBC, 0x2E, 0x8E, 0x0D, 0xB9, 0x8D, 0x2E, 0x62, 0xAD, 0x4E, 0xA4, 0x5C, 0x55, 0x36, 0xA2, 0x50, 
0x39, 0x20, 0xB2, 0x01, 0x02, 0x10, 0x0B, 0x2F, 0xCD, 0xB8, 0x8A, 0x69, 0xE2, 0xBE, 0xB7, 0x58, 
0xD2, 0xE3, 0xAE, 0x0E, 0xE8, 0xCF, 0x51, 0x42, 0x9A, 0x50, 0x13, 0x1C, 0xBB, 0xB7, 0x58, 0x7A, 
0x7B, 0x49, 0xD0, 0x16, 0xB2, 0x2F, 0x30, 0x76, 0x27, 0xA7, 0x4B, 0x6B, 0x99, 0x24, 0xEB, 0x44, 
0x81, 0x48, 0xF3, 0x1B, 0xBE, 0x54, 0x6B, 0x05, 0x80, 0x94, 0xBC, 0x01, 0xAC, 0xF6, 0x74, 0x50, 
0x18, 0x31, 0xF5, 0x0D, 0xE1, 0xE6, 0xBD, 0x41, 0x83, 0x81, 0xA1, 0x62, 0x4F, 0x79, 0xB5, 0x47, 
0x16, 0xD4, 0xBE, 0x5A, 0x32, 0x1F, 0x27, 0x40, 0x4F, 0x39, 0x36, 0x54, 0x2A, 0xA0, 0x05, 0x38, 
0xE5, 0x15, 0xB2, 0x3C, 0xDC, 0x89, 0xAF, 0x51, 0x9D, 0x08, 0xFD, 0x02, 0xA1, 0x44, 0x40, 0x4A, 
0x95, 0x17, 0xDE, 0x33, 0x4B, 0x57, 0xA1, 0x5F, 0xF6, 0x7A, 0xE2, 0x1A, 0xCE, 0x37, 0x90, 0x35, 
0x4D, 0x67, 0xAC, 0x0E, 0xC3, 0x33, 0x6D, 0x04, 0xB1, 0xF6, 0x47, 0x0E, 0x1F, 0x4B, 0x5B, 0x1D, 
0xAC, 0x03, 0xBF, 0x46, 0x4B, 0x47, 0x5B, 0x2A, 0xDA, 0x02, 0xB4, 0x17, 0x27, 0x4D, 0x8F, 0x5D, 
0xFD, 0x76, 0x8B, 0x20, 0x02, 0xAA, 0xFF, 0x02, 0xC0, 0x71, 0x7A, 0x22, 0x7E, 0xBF, 0x7E, 0x3C, 
0xC0, 0xFE, 0x6A, 0x08, 0x40, 0x06, 0x37, 0x24, 0x2A, 0xB6, 0xF3, 0x0C, 0xD8, 0x2F, 0x60, 0x16, 
0x21, 0xED, 0xF4, 0x65, 0xAE, 0x37, 0x95, 0x6F, 0x27, 0xA9, 0x15, 0x5E, 0x37, 0xC1, 0xB3, 0x40, 
0xE0, 0x56, 0xBC, 0x2F, 0x76, 0xE2, 0x4B, 0x32, 0x62, 0x61, 0xB9, 0x78, 0xC5, 0x6C, 0x6E, 0x6C, 
0x53, 0x6C, 0xFB, 0x03, 0xFF, 0x69, 0xB6, 0x7B, 0x66, 0xB1, 0xAE, 0x36, 0xE8, 0x83, 0xD9, 0x37, 
0x4A, 0xC1, 0x57, 0x5B, 0x5C, 0x2C, 0x91, 0x51, 0xB7, 0xBB, 0x69, 0x6D, 0x97, 0x28, 0x04, 0x6A, 
0x20, 0x60, 0xFE, 0x55, 0x68, 0xB2, 0xB1, 0x7B, 0xB7, 0x73, 0x5F, 0x07, 0xCC, 0x63, 0xBD, 0x1C, 
0xB3, 0xF9, 0x0C, 0x26, 0x91, 0x76, 0x13, 0x1F, 0xF3, 0xB0, 0x4C, 0x7A, 0xB0, 0x70, 0x98, 0x46, 
0x93, 0x20, 0x13, 0x22, 0xB3, 0x22, 0xC7, 0x1C, 0x2F, 0x30, 0x17, 0x03, 0x53, 0x1F, 0x7E, 0x2A, 
0xF4, 0x28, 0xFE, 0x40, 0x59, 0xE6, 0x0A, 0x10, 0x2B, 0x4F, 0xDE, 0x40, 0x15, 0x16, 0xF3, 0x26, 
0x07, 0x1E, 0xA0, 0x7F, 0x53, 0xF8, 0xF3, 0x1E, 0x4D, 0xD7, 0xA6, 0x67, 0xE7, 0x74, 0x5C, 0x2F, 
};

/* 84C8AAE13B264F90401EB5864367ECFDAAE29BCE5FB6ED6AAACEDC5D0EEA5353452393FAA4F2FE5D73F840764ECC8270A4AE78CFFA7576BF92831AB84E3875BDF8347FA023461E1A1D0089696C4FA9DAB1E494F2AF8A41E7B37AFF048496F2F3D66D392A9783E097B8284ECEC73D2A52D83E55DF2FD0BFACC850AACB76E681C628D1A5DE5C185ECB74938ABCC7FC90DEFC5D23CAA78EB2EEC2C45071C737EA06F4396E15091F6132207254D6C8B9EAF1D0384E47F894019190010DC56BC4DFADDC0AC0AFBC9A0CBA86EF187D337D78D2ED51F84F39CAA913239590E27FDEBABF37DF14E473CEE8C67DD627C0C1C726DFF60C5C0C273BDD42CE1B6217EBF5D93DFC6B289AE2A36EC99B05A207069A97BE56B83857F39316392D8CEEDE61D7005361461CD67DB27122798181469F8806C776527F65F1D2B62FF3233F3B8382B329E57779DA9452670F836E2927BEE2189F34B81E4301FC563A443BF66495D38B58DF80B54D043D8DF03A9164C2F9BDB601F73891C51E4B3A5E757E760398D712E3 */
static UCHAR secret_3072[] = {
0x84, 0xC8, 0xAA, 0xE1, 0x3B, 0x26, 0x4F, 0x90, 0x40, 0x1E, 0xB5, 0x86, 0x43, 0x67, 0xEC, 0xFD, 
0xAA, 0xE2, 0x9B, 0xCE, 0x5F, 0xB6, 0xED, 0x6A, 0xAA, 0xCE, 0xDC, 0x5D, 0x0E, 0xEA, 0x53, 0x53, 
0x45, 0x23, 0x93, 0xFA, 0xA4, 0xF2, 0xFE, 0x5D, 0x73, 0xF8, 0x40, 0x76, 0x4E, 0xCC, 0x82, 0x70, 
0xA4, 0xAE, 0x78, 0xCF, 0xFA, 0x75, 0x76, 0xBF, 0x92, 0x83, 0x1A, 0xB8, 0x4E, 0x38, 0x75, 0xBD, 
0xF8, 0x34, 0x7F, 0xA0, 0x23, 0x46, 0x1E, 0x1A, 0x1D, 0x00, 0x89, 0x69, 0x6C, 0x4F, 0xA9, 0xDA, 
0xB1, 0xE4, 0x94, 0xF2, 0xAF, 0x8A, 0x41, 0xE7, 0xB3, 0x7A, 0xFF, 0x04, 0x84, 0x96, 0xF2, 0xF3, 
0xD6, 0x6D, 0x39, 0x2A, 0x97, 0x83, 0xE0, 0x97, 0xB8, 0x28, 0x4E, 0xCE, 0xC7, 0x3D, 0x2A, 0x52, 
0xD8, 0x3E, 0x55, 0xDF, 0x2F, 0xD0, 0xBF, 0xAC, 0xC8, 0x50, 0xAA, 0xCB, 0x76, 0xE6, 0x81, 0xC6, 
0x28, 0xD1, 0xA5, 0xDE, 0x5C, 0x18, 0x5E, 0xCB, 0x74, 0x93, 0x8A, 0xBC, 0xC7, 0xFC, 0x90, 0xDE, 
0xFC, 0x5D, 0x23, 0xCA, 0xA7, 0x8E, 0xB2, 0xEE, 0xC2, 0xC4, 0x50, 0x71, 0xC7, 0x37, 0xEA, 0x06, 
0xF4, 0x39, 0x6E, 0x15, 0x09, 0x1F, 0x61, 0x32, 0x20, 0x72, 0x54, 0xD6, 0xC8, 0xB9, 0xEA, 0xF1, 
0xD0, 0x38, 0x4E, 0x47, 0xF8, 0x94, 0x01, 0x91, 0x90, 0x01, 0x0D, 0xC5, 0x6B, 0xC4, 0xDF, 0xAD, 
0xDC, 0x0A, 0xC0, 0xAF, 0xBC, 0x9A, 0x0C, 0xBA, 0x86, 0xEF, 0x18, 0x7D, 0x33, 0x7D, 0x78, 0xD2, 
0xED, 0x51, 0xF8, 0x4F, 0x39, 0xCA, 0xA9, 0x13, 0x23, 0x95, 0x90, 0xE2, 0x7F, 0xDE, 0xBA, 0xBF, 
0x37, 0xDF, 0x14, 0xE4, 0x73, 0xCE, 0xE8, 0xC6, 0x7D, 0xD6, 0x27, 0xC0, 0xC1, 0xC7, 0x26, 0xDF, 
0xF6, 0x0C, 0x5C, 0x0C, 0x27, 0x3B, 0xDD, 0x42, 0xCE, 0x1B, 0x62, 0x17, 0xEB, 0xF5, 0xD9, 0x3D, 
0xFC, 0x6B, 0x28, 0x9A, 0xE2, 0xA3, 0x6E, 0xC9, 0x9B, 0x05, 0xA2, 0x07, 0x06, 0x9A, 0x97, 0xBE, 
0x56, 0xB8, 0x38, 0x57, 0xF3, 0x93, 0x16, 0x39, 0x2D, 0x8C, 0xEE, 0xDE, 0x61, 0xD7, 0x00, 0x53, 
0x61, 0x46, 0x1C, 0xD6, 0x7D, 0xB2, 0x71, 0x22, 0x79, 0x81, 0x81, 0x46, 0x9F, 0x88, 0x06, 0xC7, 
0x76, 0x52, 0x7F, 0x65, 0xF1, 0xD2, 0xB6, 0x2F, 0xF3, 0x23, 0x3F, 0x3B, 0x83, 0x82, 0xB3, 0x29, 
0xE5, 0x77, 0x79, 0xDA, 0x94, 0x52, 0x67, 0x0F, 0x83, 0x6E, 0x29, 0x27, 0xBE, 0xE2, 0x18, 0x9F, 
0x34, 0xB8, 0x1E, 0x43, 0x01, 0xFC, 0x56, 0x3A, 0x44, 0x3B, 0xF6, 0x64, 0x95, 0xD3, 0x8B, 0x58, 
0xDF, 0x80, 0xB5, 0x4D, 0x04, 0x3D, 0x8D, 0xF0, 0x3A, 0x91, 0x64, 0xC2, 0xF9, 0xBD, 0xB6, 0x01, 
0xF7, 0x38, 0x91, 0xC5, 0x1E, 0x4B, 0x3A, 0x5E, 0x75, 0x7E, 0x76, 0x03, 0x98, 0xD7, 0x12, 0xE3, 
};

/* 00010001 */
static UCHAR pub_e_4096[] = {
0x00, 0x01, 0x00, 0x01, };

/* 484776A2A856E1B7D3F09A6ECF889E30D76353318B0E002D2B546AED3FBB52EA53453F2B94B217804F0E858DF2E842F67E30DCDEB051377837E0732BD8ABE0A55AFDF183FBC10FEE1F7037DE84A94473F8DFBE7F43FC3E73B3E49996B88B3C4D2589C029E8BE7FF27C1B27F22D64ECFA508DC4F282086310D546DC5A7CDA38E340442F931F401346FB22C3B9EF07AA7E7E2AA6F508DE1AAF2ACBB4A337CA0C9847A50C3870944185D382F7CD2FD18E90F46B15883F50E874D2B76D4A3458B2BA0657E3496075E099DB3CD5E8B080C39F6C7829C3772605B0E461D90A563B16852A69C0AA978222AEADBBF69ED997EE535BD2139968A22CF61983FF663EF8AFDFE6B1B72988FA01EE711F0707A992C31DB7CCC91B1EC21D1F271872D9ED41D4BE0DBBF5C03C92AAD7B04C88489FA064786CC040B076A6D4D481EF9391271DB35EBFA408BD55A4F1835743E8F23E11D62361512598EEE25F2E5D81B892D84BA2EF3E6F9D424EA363BA21B2289226B109953604CA8AD8B3D6808E83AD4DE01A80AB7C41838746005366AA5CB263FD6656B8D4D8393AC53FC732700A0C3F04CEB927EA4133976786088C08396154FF72A1810AC9D7488A098CEA914D3FEAD4D17C0A89F92449A6EB2D363525B608CEC286E764249F42AFAC0AD7FD0AA756A3CDDA5FA752461DD999C3E7EC9E82BAF2F8C5EF7DF23C8FCB1AC1496A3A93AECA0AA309 */
static UCHAR pri_e_4096[] = {
0x48, 0x47, 0x76, 0xA2, 0xA8, 0x56, 0xE1, 0xB7, 0xD3, 0xF0, 0x9A, 0x6E, 0xCF, 0x88, 0x9E, 0x30, 
0xD7, 0x63, 0x53, 0x31, 0x8B, 0x0E, 0x00, 0x2D, 0x2B, 0x54, 0x6A, 0xED, 0x3F, 0xBB, 0x52, 0xEA, 
0x53, 0x45, 0x3F, 0x2B, 0x94, 0xB2, 0x17, 0x80, 0x4F, 0x0E, 0x85, 0x8D, 0xF2, 0xE8, 0x42, 0xF6, 
0x7E, 0x30, 0xDC, 0xDE, 0xB0, 0x51, 0x37, 0x78, 0x37, 0xE0, 0x73, 0x2B, 0xD8, 0xAB, 0xE0, 0xA5, 
0x5A, 0xFD, 0xF1, 0x83, 0xFB, 0xC1, 0x0F, 0xEE, 0x1F, 0x70, 0x37, 0xDE, 0x84, 0xA9, 0x44, 0x73, 
0xF8, 0xDF, 0xBE, 0x7F, 0x43, 0xFC, 0x3E, 0x73, 0xB3, 0xE4, 0x99, 0x96, 0xB8, 0x8B, 0x3C, 0x4D, 
0x25, 0x89, 0xC0, 0x29, 0xE8, 0xBE, 0x7F, 0xF2, 0x7C, 0x1B, 0x27, 0xF2, 0x2D, 0x64, 0xEC, 0xFA, 
0x50, 0x8D, 0xC4, 0xF2, 0x82, 0x08, 0x63, 0x10, 0xD5, 0x46, 0xDC, 0x5A, 0x7C, 0xDA, 0x38, 0xE3, 
0x40, 0x44, 0x2F, 0x93, 0x1F, 0x40, 0x13, 0x46, 0xFB, 0x22, 0xC3, 0xB9, 0xEF, 0x07, 0xAA, 0x7E, 
0x7E, 0x2A, 0xA6, 0xF5, 0x08, 0xDE, 0x1A, 0xAF, 0x2A, 0xCB, 0xB4, 0xA3, 0x37, 0xCA, 0x0C, 0x98, 
0x47, 0xA5, 0x0C, 0x38, 0x70, 0x94, 0x41, 0x85, 0xD3, 0x82, 0xF7, 0xCD, 0x2F, 0xD1, 0x8E, 0x90, 
0xF4, 0x6B, 0x15, 0x88, 0x3F, 0x50, 0xE8, 0x74, 0xD2, 0xB7, 0x6D, 0x4A, 0x34, 0x58, 0xB2, 0xBA, 
0x06, 0x57, 0xE3, 0x49, 0x60, 0x75, 0xE0, 0x99, 0xDB, 0x3C, 0xD5, 0xE8, 0xB0, 0x80, 0xC3, 0x9F, 
0x6C, 0x78, 0x29, 0xC3, 0x77, 0x26, 0x05, 0xB0, 0xE4, 0x61, 0xD9, 0x0A, 0x56, 0x3B, 0x16, 0x85, 
0x2A, 0x69, 0xC0, 0xAA, 0x97, 0x82, 0x22, 0xAE, 0xAD, 0xBB, 0xF6, 0x9E, 0xD9, 0x97, 0xEE, 0x53, 
0x5B, 0xD2, 0x13, 0x99, 0x68, 0xA2, 0x2C, 0xF6, 0x19, 0x83, 0xFF, 0x66, 0x3E, 0xF8, 0xAF, 0xDF, 
0xE6, 0xB1, 0xB7, 0x29, 0x88, 0xFA, 0x01, 0xEE, 0x71, 0x1F, 0x07, 0x07, 0xA9, 0x92, 0xC3, 0x1D, 
0xB7, 0xCC, 0xC9, 0x1B, 0x1E, 0xC2, 0x1D, 0x1F, 0x27, 0x18, 0x72, 0xD9, 0xED, 0x41, 0xD4, 0xBE, 
0x0D, 0xBB, 0xF5, 0xC0, 0x3C, 0x92, 0xAA, 0xD7, 0xB0, 0x4C, 0x88, 0x48, 0x9F, 0xA0, 0x64, 0x78, 
0x6C, 0xC0, 0x40, 0xB0, 0x76, 0xA6, 0xD4, 0xD4, 0x81, 0xEF, 0x93, 0x91, 0x27, 0x1D, 0xB3, 0x5E, 
0xBF, 0xA4, 0x08, 0xBD, 0x55, 0xA4, 0xF1, 0x83, 0x57, 0x43, 0xE8, 0xF2, 0x3E, 0x11, 0xD6, 0x23, 
0x61, 0x51, 0x25, 0x98, 0xEE, 0xE2, 0x5F, 0x2E, 0x5D, 0x81, 0xB8, 0x92, 0xD8, 0x4B, 0xA2, 0xEF, 
0x3E, 0x6F, 0x9D, 0x42, 0x4E, 0xA3, 0x63, 0xBA, 0x21, 0xB2, 0x28, 0x92, 0x26, 0xB1, 0x09, 0x95, 
0x36, 0x04, 0xCA, 0x8A, 0xD8, 0xB3, 0xD6, 0x80, 0x8E, 0x83, 0xAD, 0x4D, 0xE0, 0x1A, 0x80, 0xAB, 
0x7C, 0x41, 0x83, 0x87, 0x46, 0x00, 0x53, 0x66, 0xAA, 0x5C, 0xB2, 0x63, 0xFD, 0x66, 0x56, 0xB8, 
0xD4, 0xD8, 0x39, 0x3A, 0xC5, 0x3F, 0xC7, 0x32, 0x70, 0x0A, 0x0C, 0x3F, 0x04, 0xCE, 0xB9, 0x27, 
0xEA, 0x41, 0x33, 0x97, 0x67, 0x86, 0x08, 0x8C, 0x08, 0x39, 0x61, 0x54, 0xFF, 0x72, 0xA1, 0x81, 
0x0A, 0xC9, 0xD7, 0x48, 0x8A, 0x09, 0x8C, 0xEA, 0x91, 0x4D, 0x3F, 0xEA, 0xD4, 0xD1, 0x7C, 0x0A, 
0x89, 0xF9, 0x24, 0x49, 0xA6, 0xEB, 0x2D, 0x36, 0x35, 0x25, 0xB6, 0x08, 0xCE, 0xC2, 0x86, 0xE7, 
0x64, 0x24, 0x9F, 0x42, 0xAF, 0xAC, 0x0A, 0xD7, 0xFD, 0x0A, 0xA7, 0x56, 0xA3, 0xCD, 0xDA, 0x5F, 
0xA7, 0x52, 0x46, 0x1D, 0xD9, 0x99, 0xC3, 0xE7, 0xEC, 0x9E, 0x82, 0xBA, 0xF2, 0xF8, 0xC5, 0xEF, 
0x7D, 0xF2, 0x3C, 0x8F, 0xCB, 0x1A, 0xC1, 0x49, 0x6A, 0x3A, 0x93, 0xAE, 0xCA, 0x0A, 0xA3, 0x09, 
};

/* CD0B9F926AB52E04736D716641FE6C6425E65D450FB54DB70909AA6D33CCE0CA8BC0A45DEC6D4382FCAF501420F545994FB6302D2E353B6F0CA98880AC99613253C44CB43224B29F13766831C8754BF70446343F96FCFDA0C56D7A38184FD6C82604CE5473129A2127E37FDEE76968CC667B1034342891D7E3B7973AF1947C869580A6EBC0A68369E5BC0972F18BA9CFBCBA224287B6D06E577C42631DA539EFC796B9EA8E33ED957429B3CE8766432E329EA9509205184986BAA741C646C6EACD61337410099603FCCB49B51E0088860A52BC4A087B8B2596AB6079CC27C49B6A148E825CBC7EB0BAA362F15A57E55A4ABF85CA311B90D5E578F38C1DAFA02EB9D61E465EE0D1F60352590E801C5690BBFD7690867DCC1D07ACA0C182910445E493D442710355932B8CE7263E5EEDB835934D8DF2DC8BED771332ED974A759DE21D8BD9DBB8FD5CE83177FE43C71DAB3143CE893A8438203F5E4714DBB417A7A285BDDEF0E5E6AC22591EDD151E1EC5E7FC111501F51DD7089C95C70347DC982E4BE16EDE57459A4C5130A9232CB61836DA1148AE6E5C7619A35DEC2A4F2D4C5258552EF7C95F95A95831E06D85C05DE67E4317389369DD27B208A0E2BE94A73EF46C16654FD515B61713994C3A5B33217F3F13B12E39B2C7C666A4ACB93AF9A69F5C6550E6B41E0B01894E601765AC10CE1F9E4F7888DFE67DC1AF80B073FD */
static UCHAR m_4096[] = {
0xCD, 0x0B, 0x9F, 0x92, 0x6A, 0xB5, 0x2E, 0x04, 0x73, 0x6D, 0x71, 0x66, 0x41, 0xFE, 0x6C, 0x64, 
0x25, 0xE6, 0x5D, 0x45, 0x0F, 0xB5, 0x4D, 0xB7, 0x09, 0x09, 0xAA, 0x6D, 0x33, 0xCC, 0xE0, 0xCA, 
0x8B, 0xC0, 0xA4, 0x5D, 0xEC, 0x6D, 0x43, 0x82, 0xFC, 0xAF, 0x50, 0x14, 0x20, 0xF5, 0x45, 0x99, 
0x4F, 0xB6, 0x30, 0x2D, 0x2E, 0x35, 0x3B, 0x6F, 0x0C, 0xA9, 0x88, 0x80, 0xAC, 0x99, 0x61, 0x32, 
0x53, 0xC4, 0x4C, 0xB4, 0x32, 0x24, 0xB2, 0x9F, 0x13, 0x76, 0x68, 0x31, 0xC8, 0x75, 0x4B, 0xF7, 
0x04, 0x46, 0x34, 0x3F, 0x96, 0xFC, 0xFD, 0xA0, 0xC5, 0x6D, 0x7A, 0x38, 0x18, 0x4F, 0xD6, 0xC8, 
0x26, 0x04, 0xCE, 0x54, 0x73, 0x12, 0x9A, 0x21, 0x27, 0xE3, 0x7F, 0xDE, 0xE7, 0x69, 0x68, 0xCC, 
0x66, 0x7B, 0x10, 0x34, 0x34, 0x28, 0x91, 0xD7, 0xE3, 0xB7, 0x97, 0x3A, 0xF1, 0x94, 0x7C, 0x86, 
0x95, 0x80, 0xA6, 0xEB, 0xC0, 0xA6, 0x83, 0x69, 0xE5, 0xBC, 0x09, 0x72, 0xF1, 0x8B, 0xA9, 0xCF, 
0xBC, 0xBA, 0x22, 0x42, 0x87, 0xB6, 0xD0, 0x6E, 0x57, 0x7C, 0x42, 0x63, 0x1D, 0xA5, 0x39, 0xEF, 
0xC7, 0x96, 0xB9, 0xEA, 0x8E, 0x33, 0xED, 0x95, 0x74, 0x29, 0xB3, 0xCE, 0x87, 0x66, 0x43, 0x2E, 
0x32, 0x9E, 0xA9, 0x50, 0x92, 0x05, 0x18, 0x49, 0x86, 0xBA, 0xA7, 0x41, 0xC6, 0x46, 0xC6, 0xEA, 
0xCD, 0x61, 0x33, 0x74, 0x10, 0x09, 0x96, 0x03, 0xFC, 0xCB, 0x49, 0xB5, 0x1E, 0x00, 0x88, 0x86, 
0x0A, 0x52, 0xBC, 0x4A, 0x08, 0x7B, 0x8B, 0x25, 0x96, 0xAB, 0x60, 0x79, 0xCC, 0x27, 0xC4, 0x9B, 
0x6A, 0x14, 0x8E, 0x82, 0x5C, 0xBC, 0x7E, 0xB0, 0xBA, 0xA3, 0x62, 0xF1, 0x5A, 0x57, 0xE5, 0x5A, 
0x4A, 0xBF, 0x85, 0xCA, 0x31, 0x1B, 0x90, 0xD5, 0xE5, 0x78, 0xF3, 0x8C, 0x1D, 0xAF, 0xA0, 0x2E, 
0xB9, 0xD6, 0x1E, 0x46, 0x5E, 0xE0, 0xD1, 0xF6, 0x03, 0x52, 0x59, 0x0E, 0x80, 0x1C, 0x56, 0x90, 
0xBB, 0xFD, 0x76, 0x90, 0x86, 0x7D, 0xCC, 0x1D, 0x07, 0xAC, 0xA0, 0xC1, 0x82, 0x91, 0x04, 0x45, 
0xE4, 0x93, 0xD4, 0x42, 0x71, 0x03, 0x55, 0x93, 0x2B, 0x8C, 0xE7, 0x26, 0x3E, 0x5E, 0xED, 0xB8, 
0x35, 0x93, 0x4D, 0x8D, 0xF2, 0xDC, 0x8B, 0xED, 0x77, 0x13, 0x32, 0xED, 0x97, 0x4A, 0x75, 0x9D, 
0xE2, 0x1D, 0x8B, 0xD9, 0xDB, 0xB8, 0xFD, 0x5C, 0xE8, 0x31, 0x77, 0xFE, 0x43, 0xC7, 0x1D, 0xAB, 
0x31, 0x43, 0xCE, 0x89, 0x3A, 0x84, 0x38, 0x20, 0x3F, 0x5E, 0x47, 0x14, 0xDB, 0xB4, 0x17, 0xA7, 
0xA2, 0x85, 0xBD, 0xDE, 0xF0, 0xE5, 0xE6, 0xAC, 0x22, 0x59, 0x1E, 0xDD, 0x15, 0x1E, 0x1E, 0xC5, 
0xE7, 0xFC, 0x11, 0x15, 0x01, 0xF5, 0x1D, 0xD7, 0x08, 0x9C, 0x95, 0xC7, 0x03, 0x47, 0xDC, 0x98, 
0x2E, 0x4B, 0xE1, 0x6E, 0xDE, 0x57, 0x45, 0x9A, 0x4C, 0x51, 0x30, 0xA9, 0x23, 0x2C, 0xB6, 0x18, 
0x36, 0xDA, 0x11, 0x48, 0xAE, 0x6E, 0x5C, 0x76, 0x19, 0xA3, 0x5D, 0xEC, 0x2A, 0x4F, 0x2D, 0x4C, 
0x52, 0x58, 0x55, 0x2E, 0xF7, 0xC9, 0x5F, 0x95, 0xA9, 0x58, 0x31, 0xE0, 0x6D, 0x85, 0xC0, 0x5D, 
0xE6, 0x7E, 0x43, 0x17, 0x38, 0x93, 0x69, 0xDD, 0x27, 0xB2, 0x08, 0xA0, 0xE2, 0xBE, 0x94, 0xA7, 
0x3E, 0xF4, 0x6C, 0x16, 0x65, 0x4F, 0xD5, 0x15, 0xB6, 0x17, 0x13, 0x99, 0x4C, 0x3A, 0x5B, 0x33, 
0x21, 0x7F, 0x3F, 0x13, 0xB1, 0x2E, 0x39, 0xB2, 0xC7, 0xC6, 0x66, 0xA4, 0xAC, 0xB9, 0x3A, 0xF9, 
0xA6, 0x9F, 0x5C, 0x65, 0x50, 0xE6, 0xB4, 0x1E, 0x0B, 0x01, 0x89, 0x4E, 0x60, 0x17, 0x65, 0xAC, 
0x10, 0xCE, 0x1F, 0x9E, 0x4F, 0x78, 0x88, 0xDF, 0xE6, 0x7D, 0xC1, 0xAF, 0x80, 0xB0, 0x73, 0xFD, 
};

/* F8E2E5B6E3FF5B6415B1A7F0726E079CA461CFAF6B7DB8C32C541771245A4D4E720F5635B556A26E5E51E3E0C972503DF9AD939A2D1EDA73C4F5286711D7672E3809ACBAE0524B4466FE08929BFEC5E7F45F59874CBE615A7EF53FDB0372201B6E36EE681142954E6E85087A7384888E6B4867C08747CB6D295F00C280EF6952F1F35C001DE04C85E73662F4A272D8CC5B163D13902A371F68E746796E959135BCB1106C0979C971F6FC6F8E2E63A0DA74D52A17285D2B3633B864B726841A404414F326334911383F98D16435A00BDB99CC6274127B6408714361524064B1F1574D59BEBB1AAA2D13322C3F56A476C5A0DA6A42AE795AF041915EB2F626DB7F */
static UCHAR p_4096[] = {
0xF8, 0xE2, 0xE5, 0xB6, 0xE3, 0xFF, 0x5B, 0x64, 0x15, 0xB1, 0xA7, 0xF0, 0x72, 0x6E, 0x07, 0x9C, 
0xA4, 0x61, 0xCF, 0xAF, 0x6B, 0x7D, 0xB8, 0xC3, 0x2C, 0x54, 0x17, 0x71, 0x24, 0x5A, 0x4D, 0x4E, 
0x72, 0x0F, 0x56, 0x35, 0xB5, 0x56, 0xA2, 0x6E, 0x5E, 0x51, 0xE3, 0xE0, 0xC9, 0x72, 0x50, 0x3D, 
0xF9, 0xAD, 0x93, 0x9A, 0x2D, 0x1E, 0xDA, 0x73, 0xC4, 0xF5, 0x28, 0x67, 0x11, 0xD7, 0x67, 0x2E, 
0x38, 0x09, 0xAC, 0xBA, 0xE0, 0x52, 0x4B, 0x44, 0x66, 0xFE, 0x08, 0x92, 0x9B, 0xFE, 0xC5, 0xE7, 
0xF4, 0x5F, 0x59, 0x87, 0x4C, 0xBE, 0x61, 0x5A, 0x7E, 0xF5, 0x3F, 0xDB, 0x03, 0x72, 0x20, 0x1B, 
0x6E, 0x36, 0xEE, 0x68, 0x11, 0x42, 0x95, 0x4E, 0x6E, 0x85, 0x08, 0x7A, 0x73, 0x84, 0x88, 0x8E, 
0x6B, 0x48, 0x67, 0xC0, 0x87, 0x47, 0xCB, 0x6D, 0x29, 0x5F, 0x00, 0xC2, 0x80, 0xEF, 0x69, 0x52, 
0xF1, 0xF3, 0x5C, 0x00, 0x1D, 0xE0, 0x4C, 0x85, 0xE7, 0x36, 0x62, 0xF4, 0xA2, 0x72, 0xD8, 0xCC, 
0x5B, 0x16, 0x3D, 0x13, 0x90, 0x2A, 0x37, 0x1F, 0x68, 0xE7, 0x46, 0x79, 0x6E, 0x95, 0x91, 0x35, 
0xBC, 0xB1, 0x10, 0x6C, 0x09, 0x79, 0xC9, 0x71, 0xF6, 0xFC, 0x6F, 0x8E, 0x2E, 0x63, 0xA0, 0xDA, 
0x74, 0xD5, 0x2A, 0x17, 0x28, 0x5D, 0x2B, 0x36, 0x33, 0xB8, 0x64, 0xB7, 0x26, 0x84, 0x1A, 0x40, 
0x44, 0x14, 0xF3, 0x26, 0x33, 0x49, 0x11, 0x38, 0x3F, 0x98, 0xD1, 0x64, 0x35, 0xA0, 0x0B, 0xDB, 
0x99, 0xCC, 0x62, 0x74, 0x12, 0x7B, 0x64, 0x08, 0x71, 0x43, 0x61, 0x52, 0x40, 0x64, 0xB1, 0xF1, 
0x57, 0x4D, 0x59, 0xBE, 0xBB, 0x1A, 0xAA, 0x2D, 0x13, 0x32, 0x2C, 0x3F, 0x56, 0xA4, 0x76, 0xC5, 
0xA0, 0xDA, 0x6A, 0x42, 0xAE, 0x79, 0x5A, 0xF0, 0x41, 0x91, 0x5E, 0xB2, 0xF6, 0x26, 0xDB, 0x7F, 
};

/* D2E7F1182D621B5DB396FBA47C281B387F7A7C926E07A2657D61834813F8C5323D486A52A707FE72C1CACC12DC4D2D2BAD1E55F808F21D0F543C57C6B251B7EE3EEF5D9674CE013C333269D578714383C52FBBB48DCB5B519702757A816B5A620EDDA1253A4EF686E30DC0641A26B70F16D204997ACD6C12CE5F4B1C40796013E9E97D339AE7525C9A6F586F6CA4A19F26769D19ACF7098B5B6CF22300B8531F365D33F49B6829FC3D1FF826667F87208D21E208676BB0B8BF464982CDD81C6EC245F305EC6D3E51F7B3B3A135F2B2B29B4FFA9DFDE0F8AE927FB1335C5874C258901DBB66212BB87295FDE60A18418A9E7968DFE071339B9BECEB677A8ADE83 */
static UCHAR q_4096[] = {
0xD2, 0xE7, 0xF1, 0x18, 0x2D, 0x62, 0x1B, 0x5D, 0xB3, 0x96, 0xFB, 0xA4, 0x7C, 0x28, 0x1B, 0x38, 
0x7F, 0x7A, 0x7C, 0x92, 0x6E, 0x07, 0xA2, 0x65, 0x7D, 0x61, 0x83, 0x48, 0x13, 0xF8, 0xC5, 0x32, 
0x3D, 0x48, 0x6A, 0x52, 0xA7, 0x07, 0xFE, 0x72, 0xC1, 0xCA, 0xCC, 0x12, 0xDC, 0x4D, 0x2D, 0x2B, 
0xAD, 0x1E, 0x55, 0xF8, 0x08, 0xF2, 0x1D, 0x0F, 0x54, 0x3C, 0x57, 0xC6, 0xB2, 0x51, 0xB7, 0xEE, 
0x3E, 0xEF, 0x5D, 0x96, 0x74, 0xCE, 0x01, 0x3C, 0x33, 0x32, 0x69, 0xD5, 0x78, 0x71, 0x43, 0x83, 
0xC5, 0x2F, 0xBB, 0xB4, 0x8D, 0xCB, 0x5B, 0x51, 0x97, 0x02, 0x75, 0x7A, 0x81, 0x6B, 0x5A, 0x62, 
0x0E, 0xDD, 0xA1, 0x25, 0x3A, 0x4E, 0xF6, 0x86, 0xE3, 0x0D, 0xC0, 0x64, 0x1A, 0x26, 0xB7, 0x0F, 
0x16, 0xD2, 0x04, 0x99, 0x7A, 0xCD, 0x6C, 0x12, 0xCE, 0x5F, 0x4B, 0x1C, 0x40, 0x79, 0x60, 0x13, 
0xE9, 0xE9, 0x7D, 0x33, 0x9A, 0xE7, 0x52, 0x5C, 0x9A, 0x6F, 0x58, 0x6F, 0x6C, 0xA4, 0xA1, 0x9F, 
0x26, 0x76, 0x9D, 0x19, 0xAC, 0xF7, 0x09, 0x8B, 0x5B, 0x6C, 0xF2, 0x23, 0x00, 0xB8, 0x53, 0x1F, 
0x36, 0x5D, 0x33, 0xF4, 0x9B, 0x68, 0x29, 0xFC, 0x3D, 0x1F, 0xF8, 0x26, 0x66, 0x7F, 0x87, 0x20, 
0x8D, 0x21, 0xE2, 0x08, 0x67, 0x6B, 0xB0, 0xB8, 0xBF, 0x46, 0x49, 0x82, 0xCD, 0xD8, 0x1C, 0x6E, 
0xC2, 0x45, 0xF3, 0x05, 0xEC, 0x6D, 0x3E, 0x51, 0xF7, 0xB3, 0xB3, 0xA1, 0x35, 0xF2, 0xB2, 0xB2, 
0x9B, 0x4F, 0xFA, 0x9D, 0xFD, 0xE0, 0xF8, 0xAE, 0x92, 0x7F, 0xB1, 0x33, 0x5C, 0x58, 0x74, 0xC2, 
0x58, 0x90, 0x1D, 0xBB, 0x66, 0x21, 0x2B, 0xB8, 0x72, 0x95, 0xFD, 0xE6, 0x0A, 0x18, 0x41, 0x8A, 
0x9E, 0x79, 0x68, 0xDF, 0xE0, 0x71, 0x33, 0x9B, 0x9B, 0xEC, 0xEB, 0x67, 0x7A, 0x8A, 0xDE, 0x83, 
};

/* 77FB285B3307225D3C97072E838DBC7B1F177474A95FE063AFF0344EB9CEDD5311E7397376A5C63ED0A6DF1DA6F4D61C367F3E752641A75B063E3E34239A4D20E312AF1A9CC10A1C5CC58D301E74B904F26C843BCD787878578BC8618F3DF47D4BFDA8523AB3C77E3B422E5A3386F1724EC3230D1196FE2F81211863C5BE4C68449D200DBDB81F11494C096464B4940167180075F83C3E321D83725578FF39686EE20471ED2952731FF41005A4614366146BF94E25324F39C8FB9006F77DA869C2F3595524C11E3715F2616EB460DE10F139972F6D7D2A50449ED20E3D374002A730F24E7FE0006970BD3175F5F3155C9076FF18F1DE4958BAB26244D5132026AE97696903FF6B2839C8B42715B0695EFC3BAA5A564B277D8EAFA3466A1EAF4B43757970ADA3B44B0F80F23157E0723FD2D50305D77B83384F5E1B2994C95D5AFB3CA26F64507D17492A3C6BEC76391FD1CDA7678DC80E7A29AE792178FE99360CA90F63996BAB166DF2AF129D1F0F7C8A4AF56E28A5125772332F2239E25E582BA47E7FABFBE3494E92C83627E0285A01470B47DC416C7D92FED72544BC843789E52049A17ECA579C9CF7765CBB244E78FA4D10EBFA1220F08482287337F07F4F4B903739AFBE135FAE291F2119381FC677CD0D895CA3409917D255D320DD7022C84E57070A82687040EC6CAD1244462FAF943FE2731B0FE6F4A21E5A53133F */
static UCHAR plain_4096[] = {
0x77, 0xFB, 0x28, 0x5B, 0x33, 0x07, 0x22, 0x5D, 0x3C, 0x97, 0x07, 0x2E, 0x83, 0x8D, 0xBC, 0x7B, 
0x1F, 0x17, 0x74, 0x74, 0xA9, 0x5F, 0xE0, 0x63, 0xAF, 0xF0, 0x34, 0x4E, 0xB9, 0xCE, 0xDD, 0x53, 
0x11, 0xE7, 0x39, 0x73, 0x76, 0xA5, 0xC6, 0x3E, 0xD0, 0xA6, 0xDF, 0x1D, 0xA6, 0xF4, 0xD6, 0x1C, 
0x36, 0x7F, 0x3E, 0x75, 0x26, 0x41, 0xA7, 0x5B, 0x06, 0x3E, 0x3E, 0x34, 0x23, 0x9A, 0x4D, 0x20, 
0xE3, 0x12, 0xAF, 0x1A, 0x9C, 0xC1, 0x0A, 0x1C, 0x5C, 0xC5, 0x8D, 0x30, 0x1E, 0x74, 0xB9, 0x04, 
0xF2, 0x6C, 0x84, 0x3B, 0xCD, 0x78, 0x78, 0x78, 0x57, 0x8B, 0xC8, 0x61, 0x8F, 0x3D, 0xF4, 0x7D, 
0x4B, 0xFD, 0xA8, 0x52, 0x3A, 0xB3, 0xC7, 0x7E, 0x3B, 0x42, 0x2E, 0x5A, 0x33, 0x86, 0xF1, 0x72, 
0x4E, 0xC3, 0x23, 0x0D, 0x11, 0x96, 0xFE, 0x2F, 0x81, 0x21, 0x18, 0x63, 0xC5, 0xBE, 0x4C, 0x68, 
0x44, 0x9D, 0x20, 0x0D, 0xBD, 0xB8, 0x1F, 0x11, 0x49, 0x4C, 0x09, 0x64, 0x64, 0xB4, 0x94, 0x01, 
0x67, 0x18, 0x00, 0x75, 0xF8, 0x3C, 0x3E, 0x32, 0x1D, 0x83, 0x72, 0x55, 0x78, 0xFF, 0x39, 0x68, 
0x6E, 0xE2, 0x04, 0x71, 0xED, 0x29, 0x52, 0x73, 0x1F, 0xF4, 0x10, 0x05, 0xA4, 0x61, 0x43, 0x66, 
0x14, 0x6B, 0xF9, 0x4E, 0x25, 0x32, 0x4F, 0x39, 0xC8, 0xFB, 0x90, 0x06, 0xF7, 0x7D, 0xA8, 0x69, 
0xC2, 0xF3, 0x59, 0x55, 0x24, 0xC1, 0x1E, 0x37, 0x15, 0xF2, 0x61, 0x6E, 0xB4, 0x60, 0xDE, 0x10, 
0xF1, 0x39, 0x97, 0x2F, 0x6D, 0x7D, 0x2A, 0x50, 0x44, 0x9E, 0xD2, 0x0E, 0x3D, 0x37, 0x40, 0x02, 
0xA7, 0x30, 0xF2, 0x4E, 0x7F, 0xE0, 0x00, 0x69, 0x70, 0xBD, 0x31, 0x75, 0xF5, 0xF3, 0x15, 0x5C, 
0x90, 0x76, 0xFF, 0x18, 0xF1, 0xDE, 0x49, 0x58, 0xBA, 0xB2, 0x62, 0x44, 0xD5, 0x13, 0x20, 0x26, 
0xAE, 0x97, 0x69, 0x69, 0x03, 0xFF, 0x6B, 0x28, 0x39, 0xC8, 0xB4, 0x27, 0x15, 0xB0, 0x69, 0x5E, 
0xFC, 0x3B, 0xAA, 0x5A, 0x56, 0x4B, 0x27, 0x7D, 0x8E, 0xAF, 0xA3, 0x46, 0x6A, 0x1E, 0xAF, 0x4B, 
0x43, 0x75, 0x79, 0x70, 0xAD, 0xA3, 0xB4, 0x4B, 0x0F, 0x80, 0xF2, 0x31, 0x57, 0xE0, 0x72, 0x3F, 
0xD2, 0xD5, 0x03, 0x05, 0xD7, 0x7B, 0x83, 0x38, 0x4F, 0x5E, 0x1B, 0x29, 0x94, 0xC9, 0x5D, 0x5A, 
0xFB, 0x3C, 0xA2, 0x6F, 0x64, 0x50, 0x7D, 0x17, 0x49, 0x2A, 0x3C, 0x6B, 0xEC, 0x76, 0x39, 0x1F, 
0xD1, 0xCD, 0xA7, 0x67, 0x8D, 0xC8, 0x0E, 0x7A, 0x29, 0xAE, 0x79, 0x21, 0x78, 0xFE, 0x99, 0x36, 
0x0C, 0xA9, 0x0F, 0x63, 0x99, 0x6B, 0xAB, 0x16, 0x6D, 0xF2, 0xAF, 0x12, 0x9D, 0x1F, 0x0F, 0x7C, 
0x8A, 0x4A, 0xF5, 0x6E, 0x28, 0xA5, 0x12, 0x57, 0x72, 0x33, 0x2F, 0x22, 0x39, 0xE2, 0x5E, 0x58, 
0x2B, 0xA4, 0x7E, 0x7F, 0xAB, 0xFB, 0xE3, 0x49, 0x4E, 0x92, 0xC8, 0x36, 0x27, 0xE0, 0x28, 0x5A, 
0x01, 0x47, 0x0B, 0x47, 0xDC, 0x41, 0x6C, 0x7D, 0x92, 0xFE, 0xD7, 0x25, 0x44, 0xBC, 0x84, 0x37, 
0x89, 0xE5, 0x20, 0x49, 0xA1, 0x7E, 0xCA, 0x57, 0x9C, 0x9C, 0xF7, 0x76, 0x5C, 0xBB, 0x24, 0x4E, 
0x78, 0xFA, 0x4D, 0x10, 0xEB, 0xFA, 0x12, 0x20, 0xF0, 0x84, 0x82, 0x28, 0x73, 0x37, 0xF0, 0x7F, 
0x4F, 0x4B, 0x90, 0x37, 0x39, 0xAF, 0xBE, 0x13, 0x5F, 0xAE, 0x29, 0x1F, 0x21, 0x19, 0x38, 0x1F, 
0xC6, 0x77, 0xCD, 0x0D, 0x89, 0x5C, 0xA3, 0x40, 0x99, 0x17, 0xD2, 0x55, 0xD3, 0x20, 0xDD, 0x70, 
0x22, 0xC8, 0x4E, 0x57, 0x07, 0x0A, 0x82, 0x68, 0x70, 0x40, 0xEC, 0x6C, 0xAD, 0x12, 0x44, 0x46, 
0x2F, 0xAF, 0x94, 0x3F, 0xE2, 0x73, 0x1B, 0x0F, 0xE6, 0xF4, 0xA2, 0x1E, 0x5A, 0x53, 0x13, 0x3F, 
};

/* 77D78EF257A03397F5FCDB96F13035AF7DE4886130EF5E6DD4B1C92113883E5F69260331A017344C1AF65D420491A51DF570262BD14D234495D16C900CD87843F5875BD071358D67EAB294FC160971E11F015037BF9C27FCCB41E257181021C6C9D5689AE2E5538E02D210C4274DF58E4602E5F8B30B67AB23A29BBFC7596A8CE414012302141A71B14E47DBDF716018BB1E4FF0CBEC2DD339A65F5F64FBAB3C421FD87FE377874C39064F581D41A674D5668E51E22F1E55DF201F5B68921ADA20B3B5695EDA31FC826C8EEE6C064A97780075881FCD9D63A4084B49925292DDE637958F7AE48A3701BF07E25D19FDC122C44C9656813E5A5D4DE5A66912261AA2D5A3A569CF559CDAAC02FE446EA0EAB149F40F0DD83BB026D962C162F5E48EF1DF33E2D9C3CEB3A58C18630CCCF2654FA3CEE93545DA54863781B58BAAB0FAD56C921588AEFDF60CB7E34A2E809D3C82D65DACAD72AC5E21A7FA5B6557B717FF6B8419B447C1AD4E314FAE70564614FD412CDACCABFCE15C12CE08BB0D324CC5FE5F6F36CC3B89CE2538ADC26D7AF513D6A8F6A78128F060BB8919BBEF1B59BDA6267904AA2C40304FEFCB07B6B85F591435163A656405E6C3F9D5C73C354DE66520EE8A056A64C536D15C3FD9C90516729504DD9783C4A7E622F207383F46F8E9427FA96E6C1C881313BBE99F010B2C6853F3F1ED2ED49ABCFAEFECC09BE9 */
static UCHAR secret_4096[] = {
0x77, 0xD7, 0x8E, 0xF2, 0x57, 0xA0, 0x33, 0x97, 0xF5, 0xFC, 0xDB, 0x96, 0xF1, 0x30, 0x35, 0xAF, 
0x7D, 0xE4, 0x88, 0x61, 0x30, 0xEF, 0x5E, 0x6D, 0xD4, 0xB1, 0xC9, 0x21, 0x13, 0x88, 0x3E, 0x5F, 
0x69, 0x26, 0x03, 0x31, 0xA0, 0x17, 0x34, 0x4C, 0x1A, 0xF6, 0x5D, 0x42, 0x04, 0x91, 0xA5, 0x1D, 
0xF5, 0x70, 0x26, 0x2B, 0xD1, 0x4D, 0x23, 0x44, 0x95, 0xD1, 0x6C, 0x90, 0x0C, 0xD8, 0x78, 0x43, 
0xF5, 0x87, 0x5B, 0xD0, 0x71, 0x35, 0x8D, 0x67, 0xEA, 0xB2, 0x94, 0xFC, 0x16, 0x09, 0x71, 0xE1, 
0x1F, 0x01, 0x50, 0x37, 0xBF, 0x9C, 0x27, 0xFC, 0xCB, 0x41, 0xE2, 0x57, 0x18, 0x10, 0x21, 0xC6, 
0xC9, 0xD5, 0x68, 0x9A, 0xE2, 0xE5, 0x53, 0x8E, 0x02, 0xD2, 0x10, 0xC4, 0x27, 0x4D, 0xF5, 0x8E, 
0x46, 0x02, 0xE5, 0xF8, 0xB3, 0x0B, 0x67, 0xAB, 0x23, 0xA2, 0x9B, 0xBF, 0xC7, 0x59, 0x6A, 0x8C, 
0xE4, 0x14, 0x01, 0x23, 0x02, 0x14, 0x1A, 0x71, 0xB1, 0x4E, 0x47, 0xDB, 0xDF, 0x71, 0x60, 0x18, 
0xBB, 0x1E, 0x4F, 0xF0, 0xCB, 0xEC, 0x2D, 0xD3, 0x39, 0xA6, 0x5F, 0x5F, 0x64, 0xFB, 0xAB, 0x3C, 
0x42, 0x1F, 0xD8, 0x7F, 0xE3, 0x77, 0x87, 0x4C, 0x39, 0x06, 0x4F, 0x58, 0x1D, 0x41, 0xA6, 0x74, 
0xD5, 0x66, 0x8E, 0x51, 0xE2, 0x2F, 0x1E, 0x55, 0xDF, 0x20, 0x1F, 0x5B, 0x68, 0x92, 0x1A, 0xDA, 
0x20, 0xB3, 0xB5, 0x69, 0x5E, 0xDA, 0x31, 0xFC, 0x82, 0x6C, 0x8E, 0xEE, 0x6C, 0x06, 0x4A, 0x97, 
0x78, 0x00, 0x75, 0x88, 0x1F, 0xCD, 0x9D, 0x63, 0xA4, 0x08, 0x4B, 0x49, 0x92, 0x52, 0x92, 0xDD, 
0xE6, 0x37, 0x95, 0x8F, 0x7A, 0xE4, 0x8A, 0x37, 0x01, 0xBF, 0x07, 0xE2, 0x5D, 0x19, 0xFD, 0xC1, 
0x22, 0xC4, 0x4C, 0x96, 0x56, 0x81, 0x3E, 0x5A, 0x5D, 0x4D, 0xE5, 0xA6, 0x69, 0x12, 0x26, 0x1A, 
0xA2, 0xD5, 0xA3, 0xA5, 0x69, 0xCF, 0x55, 0x9C, 0xDA, 0xAC, 0x02, 0xFE, 0x44, 0x6E, 0xA0, 0xEA, 
0xB1, 0x49, 0xF4, 0x0F, 0x0D, 0xD8, 0x3B, 0xB0, 0x26, 0xD9, 0x62, 0xC1, 0x62, 0xF5, 0xE4, 0x8E, 
0xF1, 0xDF, 0x33, 0xE2, 0xD9, 0xC3, 0xCE, 0xB3, 0xA5, 0x8C, 0x18, 0x63, 0x0C, 0xCC, 0xF2, 0x65, 
0x4F, 0xA3, 0xCE, 0xE9, 0x35, 0x45, 0xDA, 0x54, 0x86, 0x37, 0x81, 0xB5, 0x8B, 0xAA, 0xB0, 0xFA, 
0xD5, 0x6C, 0x92, 0x15, 0x88, 0xAE, 0xFD, 0xF6, 0x0C, 0xB7, 0xE3, 0x4A, 0x2E, 0x80, 0x9D, 0x3C, 
0x82, 0xD6, 0x5D, 0xAC, 0xAD, 0x72, 0xAC, 0x5E, 0x21, 0xA7, 0xFA, 0x5B, 0x65, 0x57, 0xB7, 0x17, 
0xFF, 0x6B, 0x84, 0x19, 0xB4, 0x47, 0xC1, 0xAD, 0x4E, 0x31, 0x4F, 0xAE, 0x70, 0x56, 0x46, 0x14, 
0xFD, 0x41, 0x2C, 0xDA, 0xCC, 0xAB, 0xFC, 0xE1, 0x5C, 0x12, 0xCE, 0x08, 0xBB, 0x0D, 0x32, 0x4C, 
0xC5, 0xFE, 0x5F, 0x6F, 0x36, 0xCC, 0x3B, 0x89, 0xCE, 0x25, 0x38, 0xAD, 0xC2, 0x6D, 0x7A, 0xF5, 
0x13, 0xD6, 0xA8, 0xF6, 0xA7, 0x81, 0x28, 0xF0, 0x60, 0xBB, 0x89, 0x19, 0xBB, 0xEF, 0x1B, 0x59, 
0xBD, 0xA6, 0x26, 0x79, 0x04, 0xAA, 0x2C, 0x40, 0x30, 0x4F, 0xEF, 0xCB, 0x07, 0xB6, 0xB8, 0x5F, 
0x59, 0x14, 0x35, 0x16, 0x3A, 0x65, 0x64, 0x05, 0xE6, 0xC3, 0xF9, 0xD5, 0xC7, 0x3C, 0x35, 0x4D, 
0xE6, 0x65, 0x20, 0xEE, 0x8A, 0x05, 0x6A, 0x64, 0xC5, 0x36, 0xD1, 0x5C, 0x3F, 0xD9, 0xC9, 0x05, 
0x16, 0x72, 0x95, 0x04, 0xDD, 0x97, 0x83, 0xC4, 0xA7, 0xE6, 0x22, 0xF2, 0x07, 0x38, 0x3F, 0x46, 
0xF8, 0xE9, 0x42, 0x7F, 0xA9, 0x6E, 0x6C, 0x1C, 0x88, 0x13, 0x13, 0xBB, 0xE9, 0x9F, 0x01, 0x0B, 
0x2C, 0x68, 0x53, 0xF3, 0xF1, 0xED, 0x2E, 0xD4, 0x9A, 0xBC, 0xFA, 0xEF, 0xEC, 0xC0, 0x9B, 0xE9, 
};

static const UINT output_size[] = {128, 256, 384, 512};
static const UINT key_size[] = {1024, 2048, 3072, 4096};
static const UCHAR *pub_e[] = {pub_e_1024, pub_e_2048, pub_e_3072, pub_e_4096};
static const UCHAR *pri_e[] = {pri_e_1024, pri_e_2048, pri_e_3072, pri_e_4096};
static const UCHAR *m[] = {m_1024, m_2048, m_3072, m_4096};
static const UCHAR *p[] = {p_1024, p_2048, p_3072, p_4096};
static const UCHAR *q[] = {q_1024, q_2048, q_3072, q_4096};
static const UCHAR *plain[] = {plain_1024, plain_2048, plain_3072, plain_4096};
static const UCHAR *secret[] = {secret_1024, secret_2048, secret_3072, secret_4096};

static UCHAR output[512];

/**************************************************************************/
/*                                                                        */
/*  FUNCTION                                               RELEASE        */
/*                                                                        */
/*    nx_crypto_method_self_test_rsa                      PORTABLE C      */
/*                                                           6.4.3        */
/*  AUTHOR                                                                */
/*                                                                        */
/*    Timothy Stapko, Microsoft Corporation                               */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This function performs the Known Answer Test for RSA crypto method. */
/*                                                                        */
/*  INPUT                                                                 */
/*                                                                        */
/*    method_ptr                            Pointer to the crypto method  */
/*                                            to be tested.               */
/*                                                                        */
/*  OUTPUT                                                                */
/*                                                                        */
/*    status                                Completion status             */
/*                                                                        */
/*  CALLS                                                                 */
/*                                                                        */
/*    None                                                                */
/*                                                                        */
/*  CALLED BY                                                             */
/*                                                                        */
/*    Application Code                                                    */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*  05-19-2020     Timothy Stapko           Initial Version 6.0           */
/*  09-30-2020     Timothy Stapko           Modified comment(s),          */
/*                                            resulting in version 6.1    */
/*  06-02-2021     Bhupendra Naphade        Modified comment(s),          */
/*                                            renamed FIPS symbol to      */
/*                                            self-test,                  */
/*                                            resulting in version 6.1.7  */
/*                                                                        */
/**************************************************************************/
NX_CRYPTO_KEEP UINT _nx_crypto_method_self_test_rsa(NX_CRYPTO_METHOD *crypto_method,
                                                    VOID *metadata, UINT metadata_size)
{
UINT i;
UINT status;
VOID *handler = NX_CRYPTO_NULL;


    /* Validate the crypto method */
    if(crypto_method == NX_CRYPTO_NULL)
        return(NX_CRYPTO_PTR_ERROR);

    if (crypto_method -> nx_crypto_init == NX_CRYPTO_NULL)
    {
        return(NX_CRYPTO_PTR_ERROR);
    }

    if (crypto_method -> nx_crypto_operation == NX_CRYPTO_NULL)
    {
        return(NX_CRYPTO_PTR_ERROR);
    }

    for (i = 0; i < sizeof(output_size) / sizeof(UINT); ++i)
    {
        /* Encryption. */
        NX_CRYPTO_MEMSET(output, 0xFF, sizeof(output));
        status = crypto_method -> nx_crypto_init(crypto_method,
                                                 (UCHAR *)m[i],
                                                 key_size[i],
                                                 &handler,
                                                 metadata,
                                                 metadata_size);
        if (status != NX_CRYPTO_SUCCESS)
        {
            return(status);
        }

        status = crypto_method -> nx_crypto_operation(NX_CRYPTO_ENCRYPT,
                                                      handler,
                                                      crypto_method,
                                                      (UCHAR *)pub_e[i],
                                                      32,
                                                      (UCHAR *)plain[i],
                                                      output_size[i],
                                                      NX_CRYPTO_NULL,
                                                      output,
                                                      output_size[i],
                                                      metadata,
                                                      metadata_size,
                                                      NX_CRYPTO_NULL, NX_CRYPTO_NULL);
        if (status != NX_CRYPTO_SUCCESS)
        {
            return(status);
        }

        
        if (NX_CRYPTO_MEMCMP(output, secret[i], output_size[i]) != 0)
        {
            return(NX_CRYPTO_NOT_SUCCESSFUL);
        }

        if (crypto_method -> nx_crypto_cleanup)
        {
            status = crypto_method -> nx_crypto_cleanup(metadata);

            if (status != NX_CRYPTO_SUCCESS)
            {
                return(status);
            }
        }

        /* Decryption. */
        NX_CRYPTO_MEMSET(output, 0xFF, sizeof(output));
        status = crypto_method -> nx_crypto_init(crypto_method,
                                                 (UCHAR *)m[i],
                                                 key_size[i],
                                                 &handler,
                                                 metadata,
                                                 metadata_size);
        if (status != NX_CRYPTO_SUCCESS)
        {
            return(status);
        }

        status = crypto_method -> nx_crypto_operation(NX_CRYPTO_ENCRYPT,
                                                      handler,
                                                      crypto_method,
                                                      (UCHAR *)pri_e[i],
                                                      key_size[i],
                                                      (UCHAR *)secret[i],
                                                      output_size[i],
                                                      NX_CRYPTO_NULL,
                                                      output,
                                                      output_size[i],
                                                      metadata,
                                                      metadata_size,
                                                      NX_CRYPTO_NULL, NX_CRYPTO_NULL);
        if (status != NX_CRYPTO_SUCCESS)
        {
            return(status);
        }

        
        if (NX_CRYPTO_MEMCMP(output, plain[i], output_size[i]) != 0)
        {
            return(NX_CRYPTO_NOT_SUCCESSFUL);
        }

        if (crypto_method -> nx_crypto_cleanup)
        {
            status = crypto_method -> nx_crypto_cleanup(metadata);

            if (status != NX_CRYPTO_SUCCESS)
            {
                return(status);
            }
        }

        /* Decryption by CRT. */
        NX_CRYPTO_MEMSET(output, 0xFF, sizeof(output));

        status = crypto_method -> nx_crypto_init(crypto_method,
                                                 (UCHAR *)m[i],
                                                 key_size[i],
                                                 &handler,
                                                 metadata,
                                                 metadata_size);
        if (status != NX_CRYPTO_SUCCESS)
        {
            return(status);
        }

        status = crypto_method -> nx_crypto_operation(NX_CRYPTO_SET_PRIME_P,
                                                      handler,
                                                      crypto_method,
                                                      NX_CRYPTO_NULL,
                                                      0,
                                                      (UCHAR *)p[i],
                                                      output_size[i] / 2,
                                                      NX_CRYPTO_NULL,
                                                      NX_CRYPTO_NULL,
                                                      0,
                                                      metadata,
                                                      metadata_size,
                                                      NX_CRYPTO_NULL, NX_CRYPTO_NULL);
        if (status != NX_CRYPTO_SUCCESS)
        {
            return(status);
        }

        status = crypto_method -> nx_crypto_operation(NX_CRYPTO_SET_PRIME_Q,
                                                      handler,
                                                      crypto_method,
                                                      NX_CRYPTO_NULL,
                                                      0,
                                                      (UCHAR *)q[i],
                                                      output_size[i] / 2,
                                                      NX_CRYPTO_NULL,
                                                      NX_CRYPTO_NULL,
                                                      0,
                                                      metadata,
                                                      metadata_size,
                                                      NX_CRYPTO_NULL, NX_CRYPTO_NULL);
        if (status != NX_CRYPTO_SUCCESS)
        {
            return(status);
        }

        status = crypto_method -> nx_crypto_operation(NX_CRYPTO_ENCRYPT,
                                                      handler,
                                                      crypto_method,
                                                      (UCHAR *)pri_e[i],
                                                      key_size[i],
                                                      (UCHAR *)secret[i],
                                                      output_size[i],
                                                      NX_CRYPTO_NULL,
                                                      output,
                                                      output_size[i],
                                                      metadata,
                                                      metadata_size,
                                                      NX_CRYPTO_NULL, NX_CRYPTO_NULL);
        if (status != NX_CRYPTO_SUCCESS)
        {
            return(status);
        }

        
        if (NX_CRYPTO_MEMCMP(output, plain[i], output_size[i]) != 0)
        {
            return(NX_CRYPTO_NOT_SUCCESSFUL);
        }

        if (crypto_method -> nx_crypto_cleanup)
        {
            status = crypto_method -> nx_crypto_cleanup(metadata);

            if (status != NX_CRYPTO_SUCCESS)
            {
                return(status);
            }
        }
    }

    return(NX_CRYPTO_SUCCESS);
}
#endif


