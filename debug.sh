#!/bin/bash

# NetX Crypto AES Debug Script
# This script helps set up debugging environment for AES functions

echo "NetX Crypto AES Debug Helper"
echo "============================"

# Check if build directory exists
if [ ! -d "build" ]; then
    echo "Build directory not found. Building project..."
    make clean && make
    if [ $? -ne 0 ]; then
        echo "Build failed. Please check for errors."
        exit 1
    fi
fi

# Check if executable exists
if [ ! -f "build/nx_crypto_demo" ]; then
    echo "Executable not found. Building project..."
    make
    if [ $? -ne 0 ]; then
        echo "Build failed. Please check for errors."
        exit 1
    fi
fi

echo ""
echo "Available debugging options:"
echo "1. Run program normally"
echo "2. Debug with GDB (basic)"
echo "3. Debug with GDB (with AES breakpoints)"
echo "4. Run with Valgrind (memory check)"
echo "5. Show program help"
echo ""

read -p "Select option (1-5): " choice

case $choice in
    1)
        echo "Running program normally..."
        ./build/nx_crypto_demo
        ;;
    2)
        echo "Starting GDB..."
        gdb ./build/nx_crypto_demo
        ;;
    3)
        echo "Starting GDB with AES breakpoints..."
        cat > /tmp/gdb_commands << EOF
break _nx_crypto_aes_key_set
break _nx_crypto_aes_encrypt
break _nx_crypto_aes_decrypt
break main
run
EOF
        gdb -x /tmp/gdb_commands ./build/nx_crypto_demo
        rm -f /tmp/gdb_commands
        ;;
    4)
        if command -v valgrind &> /dev/null; then
            echo "Running with Valgrind..."
            valgrind --leak-check=full --show-leak-kinds=all ./build/nx_crypto_demo
        else
            echo "Valgrind not found. Please install valgrind first."
            echo "On macOS: brew install valgrind"
            echo "On Ubuntu: sudo apt-get install valgrind"
        fi
        ;;
    5)
        echo ""
        echo "NetX Crypto AES Demo Program"
        echo "============================"
        echo ""
        echo "This program demonstrates AES encryption/decryption using NetX Crypto library."
        echo ""
        echo "Features:"
        echo "- AES-128 and AES-256 CBC mode tests"
        echo "- Interactive debugging mode"
        echo "- Detailed hex output for debugging"
        echo ""
        echo "Key debugging functions:"
        echo "- _nx_crypto_aes_key_set(): Sets up AES key and key schedule"
        echo "- _nx_crypto_aes_encrypt(): Performs AES encryption"
        echo "- _nx_crypto_aes_decrypt(): Performs AES decryption"
        echo "- _nx_crypto_method_aes_operation(): High-level AES operation"
        echo ""
        echo "To debug manually with GDB:"
        echo "1. gdb ./build/nx_crypto_demo"
        echo "2. (gdb) break _nx_crypto_aes_encrypt"
        echo "3. (gdb) run"
        echo "4. (gdb) print aes_ctx"
        echo "5. (gdb) x/16xb plaintext"
        echo ""
        ;;
    *)
        echo "Invalid option. Please select 1-5."
        exit 1
        ;;
esac

echo ""
echo "Debug session completed."
