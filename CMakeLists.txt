cmake_minimum_required(VERSION 3.10)

# 设置项目名称
project(nx_crypto_demo C)

# 设置C标准
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# 设置编译选项
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra -g -O0")

# 定义必要的宏
add_definitions(
    -DNX_CRYPTO_STANDALONE_ENABLE
    -DNX_CRYPTO_SELF_TEST
    -DNX_CRYPTO_LITTLE_ENDIAN=1
)

# 设置包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/inc
    ${CMAKE_CURRENT_SOURCE_DIR}/ports/linux/gnu/inc
)

# 添加源文件
set(CRYPTO_SOURCES
    # 主程序
    ${CMAKE_CURRENT_SOURCE_DIR}/main.c

    # 核心AES相关源文件
    ${CMAKE_CURRENT_SOURCE_DIR}/src/nx_crypto_aes.c
    ${CMAKE_CURRENT_SOURCE_DIR}/src/nx_crypto_cbc.c
    ${CMAKE_CURRENT_SOURCE_DIR}/src/nx_crypto_ctr.c
    ${CMAKE_CURRENT_SOURCE_DIR}/src/nx_crypto_gcm.c
    ${CMAKE_CURRENT_SOURCE_DIR}/src/nx_crypto_ccm.c
    ${CMAKE_CURRENT_SOURCE_DIR}/src/nx_crypto_xcbc_mac.c

    # 初始化
    ${CMAKE_CURRENT_SOURCE_DIR}/src/nx_crypto_initialize.c

    # 空加密
    ${CMAKE_CURRENT_SOURCE_DIR}/src/nx_crypto_null_cipher.c
        main.c
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${CRYPTO_SOURCES})

# 链接数学库
target_link_libraries(${PROJECT_NAME} m)
